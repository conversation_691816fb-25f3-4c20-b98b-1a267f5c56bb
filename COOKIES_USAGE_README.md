# Cloudflare绕过和Cookies使用指南

## 问题描述

在使用CloudflareBypasser绕过5秒盾后，原来的代码存在以下问题：

1. **浏览器会话结束**：每次调用`fetch_page_data`后都会关闭浏览器，导致cookies丢失
2. **重复绕过**：每次API调用都需要重新绕过Cloudflare，效率低下
3. **资源浪费**：频繁创建和销毁浏览器实例

## 解决方案

修改后的系统提供了以下功能：

### 1. Cookies持久化
- 自动保存绕过Cloudflare后获得的cookies到本地文件
- 支持多域名cookies管理
- 自动处理cookies过期和更新

### 2. 智能请求策略
- 优先使用缓存的cookies进行API请求
- 当cookies失效时自动重新绕过Cloudflare
- 保持浏览器实例活跃，避免重复创建

### 3. 高效的API调用
- 首次访问：绕过Cloudflare并保存cookies
- 后续访问：直接使用cookies，速度更快
- 自动降级：cookies失效时自动重新绕过

## 主要函数说明

### `fetch_page_data(url, use_cached_cookies=True)`
- **用途**：绕过Cloudflare并提取页面数据
- **参数**：
  - `url`: 要访问的URL
  - `use_cached_cookies`: 是否优先使用缓存的cookies
- **返回**：解析后的JSON数据
- **使用场景**：首次访问或需要强制绕过Cloudflare时

### `fetch_with_cookies(url)`
- **用途**：使用保存的cookies直接请求API
- **参数**：`url` - API URL
- **返回**：解析后的JSON数据
- **使用场景**：后续API调用，效率更高

### `close_global_driver()`
- **用途**：关闭全局浏览器实例
- **使用场景**：程序结束时清理资源

## 使用示例

### 基本使用流程

```python
from cloudflare_bypass_helper import (
    fetch_page_data, 
    fetch_with_cookies, 
    close_global_driver
)

try:
    # 1. 首次访问，绕过Cloudflare
    url1 = "https://gmgn.ai/api/v1/tokens/top_tokens"
    data1 = fetch_page_data(url1)
    print("首次访问成功，cookies已保存")
    
    # 2. 后续API调用，使用cookies
    url2 = "https://gmgn.ai/api/v1/tokens/rug_history/sol/address"
    data2 = fetch_with_cookies(url2)  # 更快，直接使用cookies
    
    url3 = "https://gmgn.ai/api/v1/trades/sol/address"
    data3 = fetch_with_cookies(url3)  # 继续使用cookies
    
finally:
    # 3. 程序结束时清理资源
    close_global_driver()
```

### 在现有代码中的应用

修改后的`gmgn_fetcher.py`已经更新为使用新的cookies系统：

```python
# 原来的代码
response = fetch_page_data(rug_url)

# 修改后的代码
response = fetch_with_cookies(rug_url)  # 优先使用cookies，更高效
```

## 文件说明

### 新增/修改的文件

1. **`cloudflare_bypass_helper.py`** - 核心功能模块
   - 添加了cookies管理功能
   - 支持全局浏览器实例
   - 智能请求策略

2. **`cookies_usage_example.py`** - 使用示例
   - 完整的使用演示
   - 错误处理示例
   - 性能对比

3. **`gmgn_fetcher.py`** - 已更新
   - 所有API调用改为使用`fetch_with_cookies`
   - 提高了整体性能

### 自动生成的文件

- **`cloudflare_cookies.pkl`** - cookies缓存文件
- **`extracted_data.json`** - 提取的数据备份

## 性能优化

### 速度提升
- **首次访问**：需要绕过Cloudflare（~5-10秒）
- **后续访问**：直接使用cookies（~1-2秒）
- **整体提升**：后续API调用速度提升80%以上

### 资源节约
- 减少浏览器实例创建/销毁
- 降低CPU和内存使用
- 减少网络请求次数

## 注意事项

### 1. Cookies过期处理
- 系统会自动检测cookies是否过期
- 过期时自动重新绕过Cloudflare
- 无需手动干预

### 2. 错误处理
- 网络错误时会自动重试
- 解析错误时会返回None
- 详细的日志记录

### 3. 资源清理
- 程序结束时务必调用`close_global_driver()`
- 避免浏览器进程残留

### 4. 并发使用
- 当前版本使用全局浏览器实例
- 不支持多线程并发
- 如需并发，请使用进程池

## 故障排除

### 常见问题

1. **Cookies失效频繁**
   - 检查网络连接稳定性
   - 确认目标网站没有额外的反爬措施

2. **浏览器启动失败**
   - 检查Chrome/Chromium路径设置
   - 确认浏览器版本兼容性

3. **内存使用过高**
   - 定期调用`close_global_driver()`
   - 避免长时间运行不清理

### 调试建议

1. 启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. 检查cookies状态：
```python
from cloudflare_bypass_helper import load_cookies_from_file
cookies = load_cookies_from_file("gmgn.ai")
print(f"Cookies数量: {len(cookies)}")
```

## 更新日志

- **v1.0**: 基础Cloudflare绕过功能
- **v2.0**: 添加cookies持久化和智能请求策略
- **v2.1**: 优化错误处理和资源管理

## 贡献

如有问题或建议，请提交Issue或Pull Request。
