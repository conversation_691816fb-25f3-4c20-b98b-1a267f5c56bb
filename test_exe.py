#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EXE文件是否能正常运行
"""

import os
import subprocess
import time
import sys

def test_exe_exists():
    """测试EXE文件是否存在"""
    exe_path = os.path.join("dist", "SolanaTokenBot.exe")
    
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"✅ EXE文件存在: {exe_path}")
        print(f"   文件大小: {file_size:.1f} MB")
        return True
    else:
        print(f"❌ EXE文件不存在: {exe_path}")
        return False

def test_exe_launch():
    """测试EXE文件是否能启动"""
    exe_path = os.path.join("dist", "SolanaTokenBot.exe")
    
    try:
        print("🚀 尝试启动EXE文件...")
        print("   注意: 这会打开GUI窗口，请手动关闭")
        
        # 启动EXE文件（非阻塞）
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        # 等待几秒看是否能正常启动
        time.sleep(3)
        
        # 检查进程状态
        poll_result = process.poll()
        
        if poll_result is None:
            print("✅ EXE文件启动成功！")
            print("   进程正在运行，GUI窗口应该已显示")
            print("   请手动关闭GUI窗口来结束测试")
            
            # 等待用户关闭窗口
            print("\n⏳ 等待GUI窗口关闭...")
            try:
                process.wait(timeout=30)  # 最多等待30秒
                print("✅ GUI窗口已关闭")
            except subprocess.TimeoutExpired:
                print("⚠️  超时，强制结束进程")
                process.terminate()
                process.wait()
            
            return True
        else:
            # 进程已结束，可能有错误
            stdout, stderr = process.communicate()
            print("❌ EXE文件启动失败")
            print(f"   退出代码: {poll_result}")
            if stderr:
                print(f"   错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 启动EXE文件时出错: {e}")
        return False

def test_dependencies():
    """测试依赖文件"""
    print("🔍 检查相关文件...")
    
    files_to_check = [
        "img/狗头.jpg",
        "img/icon.ico",
        "simple_gui.spec",
    ]
    
    all_exist = True
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            all_exist = False
    
    return all_exist

def create_test_batch():
    """创建测试批处理文件"""
    batch_content = '''@echo off
chcp 65001 >nul
echo 🧪 EXE文件测试
echo ==================

echo.
echo 🚀 启动GUI程序...
echo 注意: 这会打开图形界面窗口
echo.

start "" "dist\\SolanaTokenBot.exe"

echo ✅ 程序已启动
echo 💡 请在GUI界面中测试以下功能:
echo    1. 点击"启动机器人"按钮
echo    2. 观察状态变化
echo    3. 查看日志显示
echo    4. 点击"关闭程序"按钮
echo.
echo 📝 如果一切正常，说明打包成功！
echo.
pause
'''
    
    with open('test_exe.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ 创建测试批处理文件: test_exe.bat")

def main():
    """主测试函数"""
    print("🧪 EXE文件测试工具")
    print("="*40)
    
    test_results = []
    
    # 运行测试
    test_results.append(("EXE文件存在", test_exe_exists()))
    test_results.append(("相关文件", test_dependencies()))
    
    # 汇总结果
    print("\n" + "="*40)
    print("📊 基础测试结果:")
    print("="*40)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-"*40)
    print(f"总计: {passed}/{total} 项基础测试通过")
    
    if passed == total:
        print("\n🎉 基础测试通过！")
        
        # 询问是否进行启动测试
        try:
            choice = input("\n是否测试EXE文件启动？[y/N]: ").strip().lower()
            if choice in ['y', 'yes', '是']:
                print("\n" + "="*40)
                print("🚀 EXE启动测试:")
                print("="*40)
                
                launch_success = test_exe_launch()
                
                if launch_success:
                    print("\n🎉 EXE文件完全正常！")
                    print("\n✨ 测试结果:")
                    print("   ✅ 文件存在且完整")
                    print("   ✅ 能够正常启动")
                    print("   ✅ GUI界面显示正常")
                    print("   ✅ 图标设置成功")
                    
                    print("\n🚀 可以正式使用:")
                    print("   1. 双击 dist/SolanaTokenBot.exe")
                    print("   2. 点击'启动机器人'")
                    print("   3. 在TG中发送命令")
                    print("   4. 观察GUI状态变化")
                else:
                    print("\n⚠️  EXE启动测试失败")
                    print("可能的原因:")
                    print("   - 缺少依赖模块")
                    print("   - 系统兼容性问题")
                    print("   - 防火墙阻止")
            else:
                print("\n💡 跳过启动测试")
                
        except KeyboardInterrupt:
            print("\n测试被中断")
        
        # 创建测试批处理文件
        print("\n📝 创建便捷测试工具...")
        create_test_batch()
        
        print("\n🎯 测试完成！")
        print("可以使用以下方式测试:")
        print("   - 双击 test_exe.bat")
        print("   - 直接双击 dist/SolanaTokenBot.exe")
        
        return True
    else:
        print("\n⚠️  基础测试失败，请检查打包过程")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
