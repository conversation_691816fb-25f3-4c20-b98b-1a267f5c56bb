#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用您从F12复制的精确请求头进行测试
"""

import requests
import json
import time
import logging
from cloudflare_bypass_helper import fetch_page_data, load_cookies_from_file

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_with_exact_f12_headers():
    """使用从F12复制的精确请求头"""
    print("🧪 使用F12精确请求头测试")
    print("=" * 60)
    
    # 测试URL
    url = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10"
    
    # 1. 先用Selenium绕过获取最新cookies
    print("🔄 使用Selenium获取最新cookies...")
    result = fetch_page_data(url)
    
    if not result:
        print("❌ Selenium绕过失败")
        return False
    
    # 2. 加载cookies
    cookies = load_cookies_from_file('gmgn.ai')
    if not cookies:
        print("❌ 无法加载cookies")
        return False
    
    print(f"✅ 获取cookies: {list(cookies.keys())}")
    
    # 3. 使用您从F12复制的精确请求头
    headers = {
        # 基础头部
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'max-age=0',
        'priority': 'u=0, i',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'upgrade-insecure-requests': '1',
        
        # Sec-CH-UA 系列（Chrome特有）
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-arch': '"x86"',
        'sec-ch-ua-bitness': '"64"',
        'sec-ch-ua-full-version': '"137.0.7151.120"',
        'sec-ch-ua-full-version-list': '"Google Chrome";v="137.0.7151.120", "Chromium";v="137.0.7151.120", "Not/A)Brand";v="********"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-model': '""',
        'sec-ch-ua-platform': '"Windows"',
        'sec-ch-ua-platform-version': '"15.0.0"',
        
        # Sec-Fetch 系列（安全策略）
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
    }
    
    print(f"\n🧪 测试1: 使用完整F12请求头...")
    success1 = test_request(url, headers, cookies, "完整F12头部")
    
    # 4. 测试简化版本（只保留最关键的头部）
    essential_headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'en-US,en;q=0.9',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
    }
    
    print(f"\n🧪 测试2: 使用精简关键头部...")
    success2 = test_request(url, essential_headers, cookies, "精简关键头部")
    
    # 5. 测试添加Referer
    headers_with_referer = essential_headers.copy()
    headers_with_referer['referer'] = 'https://gmgn.ai/'
    
    print(f"\n🧪 测试3: 添加Referer头部...")
    success3 = test_request(url, headers_with_referer, cookies, "添加Referer")
    
    # 6. 测试修改sec-fetch-site
    headers_same_origin = headers_with_referer.copy()
    headers_same_origin['sec-fetch-site'] = 'same-origin'
    
    print(f"\n🧪 测试4: 修改sec-fetch-site为same-origin...")
    success4 = test_request(url, headers_same_origin, cookies, "same-origin")
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 40)
    tests = [
        ("完整F12头部", success1),
        ("精简关键头部", success2), 
        ("添加Referer", success3),
        ("same-origin", success4)
    ]
    
    for name, success in tests:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {name}: {status}")
    
    success_count = sum(1 for _, success in tests if success)
    if success_count > 0:
        print(f"\n🎉 找到了 {success_count} 个有效的请求头组合！")
        return True
    else:
        print(f"\n❌ 所有请求头组合都失败了")
        return False

def test_request(url, headers, cookies, test_name):
    """测试单个请求"""
    try:
        start_time = time.time()
        response = requests.get(url, headers=headers, cookies=cookies, timeout=15)
        end_time = time.time()
        
        request_time = end_time - start_time
        
        print(f"  响应状态码: {response.status_code}")
        print(f"  响应时间: {request_time:.2f} 秒")
        print(f"  Content-Type: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            # 检查是否被Cloudflare拦截
            if "Just a moment" in response.text or "Checking your browser" in response.text:
                print(f"  ⚠️ 仍然被Cloudflare拦截")
                return False
            
            # 尝试解析JSON
            try:
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    data = response.json()
                    if isinstance(data, dict) and 'data' in data:
                        count = len(data['data']) if isinstance(data['data'], list) else 1
                        print(f"  ✅ 成功获取JSON数据，包含 {count} 条记录")
                        
                        # 显示第一条记录
                        if isinstance(data['data'], list) and len(data['data']) > 0:
                            first_item = data['data'][0]
                            if isinstance(first_item, dict):
                                symbol = first_item.get('symbol', 'Unknown')
                                price = first_item.get('price', 'N/A')
                                print(f"  第一条记录: {symbol}, 价格: {price}")
                        
                        return True
                    else:
                        print(f"  ⚠️ JSON格式异常")
                        return False
                else:
                    # HTML响应，尝试提取JSON
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(response.text, 'html.parser')
                    pre_content = soup.find('pre')
                    
                    if pre_content and pre_content.text.strip():
                        data = json.loads(pre_content.text)
                        if isinstance(data, dict) and 'data' in data:
                            count = len(data['data']) if isinstance(data['data'], list) else 1
                            print(f"  ✅ 从HTML提取JSON数据，包含 {count} 条记录")
                            return True
                    
                    print(f"  ❌ HTML中未找到JSON数据")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
                return False
                
        else:
            print(f"  ❌ HTTP错误: {response.status_code}")
            
            # 保存错误响应用于分析
            with open(f'debug_{test_name.replace(" ", "_")}_error.html', 'w', encoding='utf-8') as f:
                f.write(response.text[:1000])
            print(f"  错误响应已保存到 debug_{test_name.replace(' ', '_')}_error.html")
            
            return False
            
    except Exception as e:
        print(f"  ❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    test_with_exact_f12_headers()
