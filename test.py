import requests

# 请求的 URL
url = "https://gmgn.ai/defi/quotation/v1/trades/sol/2ckgxZs6M3BQ4Y4VjnThknkdtCCqLxhfbt12N62Pxek6?limit=100&maker=&tag[]=smart_degen&tag[]=pump_smart"

# 请求的 headers
headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "zh-CN,zh;q=0.9",
    "cookie": "_ga=GA1.1.332182891.1733741668; _ga_0XM0LYXGC8=GS1.1.1739241201.107.1.1739243534.0.0.0; __cf_bm=3Iot0lWH2MXSjdyxdMC0vwQz48OuXy8ttoH7k4DdSv8-1739245723-*******-GloNKndrTMH81vHWDsXyQ2RYi_dVNAGAl74M0i7cXnG1rmvvEUrCarUQqvXvvjP.yDirqSgnl8UvicZs7rP8HA; cf_clearance=ljgbjVzEU.qJw48m_voyLunQv.xIxJ.MnzX.Lb82MoI-1739245793-*******-xABYQ7IA2Q9FRPvRkgAjTWPtEPOTTUf93g9JkNekZAuq5a_PkQpoVF.e.d3bEYvRQl27XPF8KJ5tDvnOTEhtFfLjV583pintJWN2KVejL9PYBZ7dYzsrqhotqSbs8l.waTxPeYcbSfTBw_p1crvyII5JhKLWlMzpHs8VBz_jWMVkoNumkPU1FWa3i3WRkxpWtZqxMbBmmvqU7c3emcyNl.KlkjO3816o6L1jh1_qrgNO7i4q569fvfNFF084DbttW2_ibfZ2l73s0w4vNxukE89VKvRYfVMWVZQk1bQIT9uDIZY0LcojbMW59RJHwZQZiBL4lw2ZEyIXlec.pPhMaA",
    # "priority": "u=0, i",
    # "sec-ch-ua": '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
    # "sec-ch-ua-arch": "x86",
    # "sec-ch-ua-bitness": "64",
    # "sec-ch-ua-full-version": "133.0.6943.59",
    # "sec-ch-ua-full-version-list": '"Not(A:Brand";v="99.0.0.0", "Google Chrome";v="133.0.6943.59", "Chromium";v="133.0.6943.59"',
    # "sec-ch-ua-mobile": "?0",
    # "sec-ch-ua-model": "",
    # "sec-ch-ua-platform": "Windows",
    # "sec-ch-ua-platform-version": "10.0.0",
    # "sec-fetch-dest": "document",
    # "sec-fetch-mode": "navigate",
    # "sec-fetch-site": "none",
    # "sec-fetch-user": "?1",
    # "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36"
}

# 发送 POST 请求
response = requests.get(url, headers=headers)
print(response.text)