import requests

bearer_token = "AAAAAAAAAAAAAAAAAAAAABPFxQEAAAAAp5IIJcBcX8VWxlYrMR9ryZjrPwI%3DsH2HOy5v0q0zZinvrhaFofO2bDQZRProUrtU1ufKbgjgkbdSYI"
username = "SuuuuuuunBo"

url = f"https://api.twitter.com/2/users/by/username/{username}?user.fields=public_metrics"
headers = {
    "Authorization": f"Bearer {bearer_token}"
}

response = requests.get(url, headers=headers)
if response.status_code == 200:
    user_data = response.json()
    public_metrics = user_data["data"]["public_metrics"]
    print(f"关注者数量: {public_metrics['followers_count']}")
    print(f"正在关注的用户数量: {public_metrics['following_count']}")
    print(f"推文数量: {public_metrics['tweet_count']}")
else:
    print(f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}")

