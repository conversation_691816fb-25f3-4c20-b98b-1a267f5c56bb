# 如何运行完整的代币分析流程（不推送TG）

## 🎯 功能概述

`run_full_process_no_tg.py` 脚本可以运行您的完整代币筛选和分析流程，包括：

1. **常规代币分析** - 筛选符合条件的代币
2. **蓝筹代币分析** - 分析蓝筹代币，发现新代币和价格翻倍代币
3. **详细数据获取** - 包括Rug检查、聪明钱包分析等
4. **结果保存** - 生成多种格式的分析报告

## 🚀 快速开始

### 运行完整流程
```bash
python run_full_process_no_tg.py
```

### 预期输出
```
🚀 开始运行完整的代币分析流程 (不推送TG)

开始时间: 2025-07-08 11:46:00
============================================================
🔍 开始常规代币分析...
   获取代币数据...
   ✅ 获取到 9 个代币
   筛选代币...
   ✅ 筛选出 1 个符合条件的代币
   ✅ 结果已保存到 regular_tokens_analysis.txt

💎 开始蓝筹代币分析...
   获取蓝筹代币数据...
   ✅ 获取到 100 个蓝筹代币
   筛选蓝筹代币...
   ✅ 筛选出 6 个符合条件的蓝筹代币
   ✅ 发现 6 个新代币
   ✅ 发现 0 个价格翻倍代币
   ✅ 结果已保存到 bluechip_tokens_analysis.txt

📄 生成总结报告...
   ✅ 总结报告已保存

🎉 代币分析流程成功完成！
```

## 📁 生成的文件

运行完成后，会生成以下文件：

### 1. `regular_tokens_analysis.txt`
- **内容**: 常规代币筛选结果
- **格式**: 可读文本，包含代币详细信息
- **示例**:
```
常规代币分析结果 - 2025-07-08 11:46:03
============================================================

<b>代币符号:</b> MEMELESS
<b>代币名称:</b> MEMELESS COIN
<b>代币地址:</b> <code>34VWJ7PPwcPpYEqTGJQXo8qaMJYoP8VKuBGHPG3ypump</code>
<b>当前价格:</b> $0.002137
<b>流动性:</b> $250.45K
<b>24小时交易量:</b> $11.88M
<b>前10持币比例:</b> 17.24%
```

### 2. `bluechip_tokens_analysis.txt`
- **内容**: 蓝筹代币详细分析
- **包含**: 安全信息、Rug检查、聪明钱包分析
- **示例**:
```
蓝筹代币分析结果 - 2025-07-08 11:46:04
============================================================

代币 1:
<b>币种:</b> Dege
<b>地址:</b> <a href='...'>...</a>
<b>价格:</b> $0.000228
<b>安全信息:</b>
- 冻结权限是否丢弃: 是
- 铸币权限是否丢弃: 是
- 是否烧池子: 是
<b>跑路次数:</b> 0 次
总聪明钱包数量: 5
总买入次数: 12
总卖出次数: 3
```

### 3. `token_analysis_summary.txt`
- **内容**: 分析结果总结
- **统计**: 各类代币数量统计
- **示例**:
```
代币分析总结报告
生成时间: 2025-07-08T11:46:03.123456

📊 统计信息:
   常规代币数量: 1
   蓝筹代币数量: 6
   新发现代币: 6
   价格翻倍代币: 0
```

### 4. `token_analysis_summary.json`
- **内容**: 完整的JSON格式数据
- **用途**: 程序化处理和进一步分析

## ⚙️ 筛选条件

### 常规代币筛选条件
- **最小流动性**: $120,000
- **最小交易量**: $200,000
- **最大前10持币比例**: 20%

### 蓝筹代币筛选条件
- **最小交易量**: $20,000
- **1分钟跌幅**: 不超过60%
- **5分钟跌幅**: 不超过80%
- **前10持币比例**: 不超过70%

### 安全筛选条件
- **必须烧池子**: burn_status = 'burn'
- **必须放弃冻结权限**: renounced_freeze_account = 1
- **必须放弃铸币权限**: renounced_mint = 1
- **跑路次数**: 不超过3次

## 🔧 自定义配置

如需修改筛选条件，编辑 `run_full_process_no_tg.py` 文件中的相关参数：

```python
# 常规代币筛选条件
filtered_tokens = filter_tokens(
    tokens,
    min_liquidity=120000,      # 最小流动性
    min_volume=200000,         # 最小交易量
    max_holders_rate=0.2       # 最大持币比例
)

# 蓝筹代币筛选条件
filtered_tokens = filter_bluechip_tokens(
    tokens, 
    min_volume=20_000          # 最小交易量
)
```

## 📊 性能数据

### 运行时间
- **首次运行**: ~30-60秒（需要绕过Cloudflare）
- **后续运行**: ~10-20秒（使用缓存cookies）

### 数据获取
- **常规代币**: 通常获取5-15个代币
- **蓝筹代币**: 获取最多100个代币
- **筛选后**: 根据市场情况，通常1-10个符合条件

## 🔄 定期运行

### 手动运行
```bash
# 单次运行
python run_full_process_no_tg.py
```

### 定时运行（Windows）
创建批处理文件 `run_analysis.bat`:
```batch
@echo off
cd /d "D:\carl\git\solanaTgbot"
python run_full_process_no_tg.py
pause
```

### 定时运行（Linux/Mac）
添加到crontab:
```bash
# 每5分钟运行一次
*/5 * * * * cd /path/to/solanaTgbot && python run_full_process_no_tg.py
```

## 🚨 注意事项

1. **网络连接**: 需要稳定的网络连接
2. **Cloudflare**: 首次运行可能需要手动完成验证
3. **资源清理**: 程序会自动清理浏览器资源
4. **文件覆盖**: 每次运行会覆盖之前的结果文件

## 🔍 故障排除

### 常见问题

1. **获取数据失败**
   - 检查网络连接
   - 等待几分钟后重试
   - 查看日志文件了解详细错误

2. **Cloudflare验证失败**
   - 手动在浏览器中访问gmgn.ai完成验证
   - 重新运行脚本

3. **筛选结果为空**
   - 正常现象，说明当前没有符合条件的代币
   - 可以调整筛选条件

### 日志查看
程序运行时会显示详细的日志信息，包括：
- 数据获取状态
- 筛选结果统计
- 错误信息和警告

## 🎯 与TG机器人的区别

| 功能 | TG机器人版本 | 无TG版本 |
|------|-------------|----------|
| 数据获取 | ✅ | ✅ |
| 代币筛选 | ✅ | ✅ |
| 安全检查 | ✅ | ✅ |
| 结果推送 | 发送到TG | 保存到文件 |
| 定时运行 | 内置定时器 | 需外部定时 |
| 价格监控 | 自动监控 | 需手动运行 |

## 📈 后续步骤

1. **查看结果**: 检查生成的分析文件
2. **手动验证**: 对感兴趣的代币进行进一步研究
3. **调整参数**: 根据结果调整筛选条件
4. **定期运行**: 设置定时任务持续监控

---

**提示**: 这个脚本完全复制了您TG机器人的分析逻辑，只是将结果保存到文件而不是发送到Telegram。您可以根据需要进一步定制和扩展功能。
