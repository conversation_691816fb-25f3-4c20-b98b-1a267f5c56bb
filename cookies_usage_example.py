#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cloudflare绕过和Cookies使用示例

这个示例展示了如何正确使用修改后的cloudflare_bypass_helper.py来：
1. 绕过Cloudflare 5秒盾
2. 保存和重用cookies
3. 高效地调用后续API接口
"""

import logging
import time
from cloudflare_bypass_helper import (
    fetch_page_data, 
    fetch_with_cookies, 
    close_global_driver,
    load_cookies_from_file,
    get_domain_from_url
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """
    主函数演示cookies的使用
    """
    # 示例URL（请替换为您实际的URL）
    base_url = "https://gmgn.ai"
    api_urls = [
        "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens/sol?orderby=volume_24h&direction=desc&filters[]=renounced_freeze_account%3A1&filters[]=renounced_mint%3A1&filters[]=burn_status%3Aburn&limit=50",
        "https://gmgn.ai/defi/quotation/v1/tokens/rug_history/sol/some_token_address",
        "https://gmgn.ai/defi/quotation/v1/trades/sol/some_token_address?limit=100&maker=&tag[]=smart_degen&tag[]=pump_smart"
    ]
    
    print("=== Cloudflare绕过和Cookies使用示例 ===\n")
    
    try:
        # 步骤1: 首次访问，绕过Cloudflare并保存cookies
        print("1. 首次访问，绕过Cloudflare...")
        first_url = api_urls[0]
        data = fetch_page_data(first_url)
        
        if data:
            print(f"✅ 成功绕过Cloudflare并获取数据")
            print(f"   数据类型: {type(data)}")
            if isinstance(data, dict):
                print(f"   数据键: {list(data.keys())[:5]}...")  # 只显示前5个键
        else:
            print("❌ 绕过Cloudflare失败")
            return
        
        # 步骤2: 检查保存的cookies
        print("\n2. 检查保存的cookies...")
        domain = get_domain_from_url(first_url)
        cookies = load_cookies_from_file(domain)
        print(f"   域名: {domain}")
        print(f"   保存的cookies数量: {len(cookies)}")
        print(f"   Cookies键: {list(cookies.keys())[:5]}...")  # 只显示前5个键
        
        # 步骤3: 使用cookies调用其他API
        print("\n3. 使用保存的cookies调用其他API...")
        
        for i, url in enumerate(api_urls[1:], 2):
            print(f"\n   3.{i-1} 调用API: {url[:60]}...")
            
            # 使用cookies直接请求
            start_time = time.time()
            api_data = fetch_with_cookies(url)
            end_time = time.time()
            
            if api_data:
                print(f"   ✅ 成功获取数据 (耗时: {end_time - start_time:.2f}秒)")
                print(f"      数据类型: {type(api_data)}")
                if isinstance(api_data, dict):
                    print(f"      数据键: {list(api_data.keys())[:3]}...")
            else:
                print(f"   ❌ 获取数据失败")
            
            # 短暂延迟避免请求过快
            time.sleep(1)
        
        # 步骤4: 演示cookies过期处理
        print("\n4. 演示cookies自动更新...")
        print("   (当cookies过期时，系统会自动重新绕过Cloudflare)")
        
        # 模拟一个可能需要重新绕过的请求
        test_url = api_urls[0]
        test_data = fetch_with_cookies(test_url)
        if test_data:
            print("   ✅ Cookies仍然有效或已自动更新")
        else:
            print("   ❌ 请求失败")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        logging.error(f"Main function error: {e}", exc_info=True)
    finally:
        # 清理资源
        print("\n5. 清理资源...")
        close_global_driver()
        print("   ✅ 浏览器实例已关闭")

def test_single_api():
    """
    测试单个API调用
    """
    print("=== 单个API测试 ===\n")
    
    url = "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens/sol?orderby=volume_24h&direction=desc&limit=10"
    
    try:
        # 方法1: 直接使用fetch_page_data（会绕过Cloudflare）
        print("方法1: 使用fetch_page_data...")
        data1 = fetch_page_data(url)
        print(f"结果: {'成功' if data1 else '失败'}")
        
        # 方法2: 使用fetch_with_cookies（会尝试使用缓存的cookies）
        print("\n方法2: 使用fetch_with_cookies...")
        data2 = fetch_with_cookies(url)
        print(f"结果: {'成功' if data2 else '失败'}")
        
    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        close_global_driver()

if __name__ == "__main__":
    # 运行主示例
    main()
    
    print("\n" + "="*50 + "\n")
    
    # 运行单个API测试
    test_single_api()
    
    print("\n=== 使用建议 ===")
    print("1. 首次访问使用 fetch_page_data() 绕过Cloudflare")
    print("2. 后续API调用使用 fetch_with_cookies() 重用cookies")
    print("3. 程序结束时调用 close_global_driver() 清理资源")
    print("4. cookies会自动保存到 cloudflare_cookies.pkl 文件")
    print("5. 当cookies过期时，系统会自动重新绕过Cloudflare")
