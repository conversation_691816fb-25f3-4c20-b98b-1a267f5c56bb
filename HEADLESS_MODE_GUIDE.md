# 无头模式使用指南

## 🎯 什么是无头模式？

无头模式（Headless Mode）是指浏览器在后台运行，不显示图形界面窗口。这样可以：

- **节省资源** - 不渲染界面，降低CPU和内存使用
- **提高速度** - 减少界面渲染开销，运行更快
- **适合自动化** - 无界面干扰，适合批量处理和服务器部署
- **后台运行** - 可以在后台静默运行，不影响其他工作

## 🚀 如何启用无头模式

### 方法1：使用专用无头模式脚本
```bash
# 直接运行无头模式脚本（推荐）
python run_headless.py
```

### 方法2：在主脚本中选择
```bash
# 运行主脚本，会询问是否使用无头模式
python run_full_process_no_tg.py
# 输入 'y' 启用无头模式，输入 'n' 使用可见模式
```

### 方法3：编程方式设置
```python
from cloudflare_bypass_helper import set_headless_mode

# 启用无头模式
set_headless_mode(True)

# 禁用无头模式（显示浏览器）
set_headless_mode(False)
```

### 方法4：环境变量设置
```bash
# Windows
set HEADLESS=true
python run_full_process_no_tg.py

# Linux/Mac
export HEADLESS=true
python run_full_process_no_tg.py
```

## 📊 模式对比

| 特性 | 无头模式 | 可见模式 |
|------|----------|----------|
| 浏览器窗口 | ❌ 不可见 | ✅ 可见 |
| 资源占用 | 🟢 较低 | 🟡 较高 |
| 运行速度 | 🟢 较快 | 🟡 较慢 |
| 调试便利性 | 🟡 较难 | 🟢 容易 |
| 服务器部署 | 🟢 适合 | ❌ 不适合 |
| 手动验证 | 🟡 困难 | 🟢 容易 |
| 批量处理 | 🟢 理想 | 🟡 一般 |

## 🛠️ 使用场景

### 🟢 推荐使用无头模式的场景：

1. **生产环境运行**
   ```bash
   # 服务器上定时运行
   python run_headless.py
   ```

2. **批量数据处理**
   ```bash
   # 处理大量代币数据
   python run_headless.py
   ```

3. **定时任务**
   ```bash
   # 设置定时任务，每5分钟运行一次
   */5 * * * * cd /path/to/project && python run_headless.py
   ```

4. **后台监控**
   ```bash
   # 在后台持续监控，不影响其他工作
   nohup python run_headless.py &
   ```

### 🟡 推荐使用可见模式的场景：

1. **开发调试**
   ```python
   set_headless_mode(False)  # 方便观察浏览器行为
   ```

2. **首次运行**
   ```bash
   # 首次运行时可能需要手动处理Cloudflare验证
   python run_full_process_no_tg.py  # 选择可见模式
   ```

3. **问题排查**
   ```python
   # 当遇到问题时，切换到可见模式观察
   set_headless_mode(False)
   ```

## ⚡ 性能优化

### 无头模式性能提升：

- **内存使用** ↓ 减少30-50%
- **CPU使用** ↓ 减少20-40%  
- **运行速度** ↑ 提升15-25%
- **网络效率** ↑ 禁用图片和JS加载

### 优化参数（自动启用）：
```python
# 无头模式自动启用的优化参数
arguments = [
    "--headless",              # 无头模式
    "--no-sandbox",           # 禁用沙盒
    "--disable-dev-shm-usage", # 减少内存使用
    "--disable-extensions",    # 禁用扩展
    "--disable-plugins",       # 禁用插件
    "--disable-images",        # 禁用图片加载
    "--disable-javascript",    # 禁用JS（某些情况下）
]
```

## 🔧 故障排除

### 常见问题：

1. **无头模式下Cloudflare验证失败**
   ```python
   # 解决方案：先用可见模式建立cookies
   set_headless_mode(False)
   fetch_page_data(url)  # 手动完成验证
   
   set_headless_mode(True)
   fetch_with_cookies(url)  # 后续使用无头模式
   ```

2. **无头模式下获取数据失败**
   ```bash
   # 检查是否有有效的cookies
   python -c "
   from cloudflare_bypass_helper import load_cookies_from_file
   cookies = load_cookies_from_file('gmgn.ai')
   print(f'Cookies数量: {len(cookies)}')
   "
   ```

3. **Chrome浏览器路径问题**
   ```bash
   # Windows - 设置Chrome路径
   set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
   
   # Linux - 安装Chrome
   sudo apt-get install google-chrome-stable
   ```

### 调试技巧：

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **测试模式切换**
   ```bash
   # 测试两种模式
   python test_headless_mode.py
   ```

3. **检查浏览器状态**
   ```python
   from cloudflare_bypass_helper import get_headless_mode
   print(f"当前无头模式: {get_headless_mode()}")
   ```

## 📝 最佳实践

### 1. 渐进式使用
```bash
# 第一次运行：使用可见模式建立cookies
python run_full_process_no_tg.py  # 选择 'n'

# 后续运行：使用无头模式
python run_headless.py
```

### 2. 定时任务设置
```bash
# Windows - 创建批处理文件
@echo off
cd /d "D:\carl\git\solanaTgbot"
python run_headless.py >> analysis.log 2>&1

# Linux - 添加到crontab
*/5 * * * * cd /path/to/project && python run_headless.py >> analysis.log 2>&1
```

### 3. 错误处理
```python
try:
    set_headless_mode(True)
    data = fetch_page_data(url)
except Exception as e:
    # 如果无头模式失败，切换到可见模式
    set_headless_mode(False)
    data = fetch_page_data(url)
```

### 4. 资源管理
```python
try:
    # 你的代码
    pass
finally:
    # 确保清理浏览器资源
    close_global_driver()
```

## 🎯 使用建议

### 开发阶段：
- 使用可见模式进行调试
- 观察Cloudflare验证过程
- 验证数据获取正确性

### 测试阶段：
- 两种模式都测试
- 验证无头模式稳定性
- 确认cookies机制正常

### 生产阶段：
- 使用无头模式运行
- 设置定时任务
- 监控日志输出

---

**总结**：无头模式是提高效率和节省资源的最佳选择，特别适合生产环境和批量处理。建议在开发调试时使用可见模式，在正式运行时切换到无头模式。
