@echo off
echo 正在打包 Solana Telegram Bot...
echo.

REM 检查是否安装了 pyinstaller
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 正在安装 PyInstaller...
    pip install pyinstaller
)

REM 清理之前的构建文件
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

REM 打包程序
echo 开始打包...
pyinstaller --onefile --noconsole --name="SolanaTgBot" main.py

REM 检查打包结果
if exist "dist\SolanaTgBot.exe" (
    echo.
    echo 打包成功！
    echo 可执行文件位置: dist\SolanaTgBot.exe
    echo.
    echo 请确保将以下文件与exe文件放在同一目录：
    echo - config.json
    echo.
    pause
) else (
    echo.
    echo 打包失败，请检查错误信息。
    pause
)
