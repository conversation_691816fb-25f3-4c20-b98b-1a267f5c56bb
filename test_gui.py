#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI界面功能
"""

import tkinter as tk
from tkinter import ttk
import os
import sys

def test_gui_components():
    """测试GUI组件"""
    print("🧪 测试GUI组件...")
    
    try:
        # 测试tkinter
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试基本组件
        frame = ttk.Frame(root)
        label = ttk.Label(frame, text="测试")
        button = ttk.Button(frame, text="测试按钮")
        
        print("   ✅ tkinter组件正常")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"   ❌ tkinter组件测试失败: {e}")
        return False

def test_icon_conversion():
    """测试图标转换"""
    print("🖼️  测试图标转换...")
    
    try:
        from PIL import Image
        
        # 检查原图片
        jpg_path = os.path.join("img", "狗头.jpg")
        if not os.path.exists(jpg_path):
            print(f"   ❌ 原图片不存在: {jpg_path}")
            return False
        
        print(f"   ✅ 原图片存在: {jpg_path}")
        
        # 测试图片加载
        img = Image.open(jpg_path)
        print(f"   ✅ 图片格式: {img.format}, 尺寸: {img.size}")
        
        # 测试调整大小
        resized = img.resize((32, 32), Image.Resampling.LANCZOS)
        print("   ✅ 图片调整大小成功")
        
        # 测试ICO转换
        ico_path = os.path.join("img", "test_icon.ico")
        icon_sizes = [(16, 16), (32, 32), (48, 48)]
        img.save(ico_path, format='ICO', sizes=icon_sizes)
        
        if os.path.exists(ico_path):
            print(f"   ✅ ICO图标生成成功: {ico_path}")
            # 清理测试文件
            os.remove(ico_path)
            return True
        else:
            print("   ❌ ICO图标生成失败")
            return False
            
    except ImportError:
        print("   ❌ PIL库未安装")
        return False
    except Exception as e:
        print(f"   ❌ 图标转换测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("📦 测试依赖包...")
    
    dependencies = {
        'tkinter': 'tkinter',
        'PIL': 'PIL',
        'telegram': 'python-telegram-bot',
        'requests': 'requests',
        'bs4': 'beautifulsoup4',
        'DrissionPage': 'DrissionPage'
    }
    
    results = {}
    
    for module, package in dependencies.items():
        try:
            __import__(module)
            print(f"   ✅ {package}")
            results[package] = True
        except ImportError:
            print(f"   ❌ {package}")
            results[package] = False
    
    return all(results.values())

def test_file_structure():
    """测试文件结构"""
    print("📁 测试文件结构...")
    
    required_files = [
        'main_gui.py',
        'TelegramBot.py',
        'cloudflare_bypass_helper.py',
        'gmgn_fetcher.py',
        'CloudflareBypasser.py',
        os.path.join('img', '狗头.jpg')
    ]
    
    all_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            all_exist = False
    
    return all_exist

def create_demo_gui():
    """创建演示GUI"""
    print("\n🎨 创建演示GUI...")
    
    try:
        root = tk.Tk()
        root.title("GUI测试 - Solana代币机器人")
        root.geometry("400x300")
        
        # 尝试设置图标
        try:
            from PIL import Image, ImageTk
            icon_path = os.path.join("img", "狗头.jpg")
            if os.path.exists(icon_path):
                img = Image.open(icon_path)
                img = img.resize((32, 32), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(img)
                root.iconphoto(False, photo)
                print("   ✅ 图标设置成功")
        except Exception as e:
            print(f"   ⚠️  图标设置失败: {e}")
        
        # 创建界面
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🚀 Solana代币分析机器人", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 状态
        status_frame = ttk.LabelFrame(main_frame, text="📊 状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(status_frame, text="机器人状态: 🟢 测试模式").pack(anchor=tk.W)
        ttk.Label(status_frame, text="无头模式: 🔇 已启用").pack(anchor=tk.W)
        ttk.Label(status_frame, text="图标状态: ✅ 已加载").pack(anchor=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Button(button_frame, text="🚀 启动机器人").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="⏹️ 停止机器人").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ 关闭", command=root.quit).pack(side=tk.RIGHT)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="📝 日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_text = tk.Text(log_frame, height=8, width=50)
        log_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加测试日志
        log_text.insert(tk.END, "[12:34:56] 🎯 GUI测试模式启动\n")
        log_text.insert(tk.END, "[12:34:57] ✅ 界面组件加载成功\n")
        log_text.insert(tk.END, "[12:34:58] 🔇 无头模式已启用\n")
        log_text.insert(tk.END, "[12:34:59] 📱 等待Telegram命令\n")
        
        print("   ✅ 演示GUI创建成功")
        print("   💡 关闭窗口继续测试...")
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"   ❌ 演示GUI创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 GUI版本功能测试")
    print("="*40)
    
    test_results = []
    
    # 运行测试
    test_results.append(("文件结构", test_file_structure()))
    test_results.append(("依赖包", test_dependencies()))
    test_results.append(("GUI组件", test_gui_components()))
    test_results.append(("图标转换", test_icon_conversion()))
    
    # 汇总结果
    print("\n" + "="*40)
    print("📊 测试结果汇总:")
    print("="*40)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:10} : {status}")
        if result:
            passed += 1
    
    print("-"*40)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！GUI版本准备就绪")
        
        # 询问是否显示演示GUI
        try:
            choice = input("\n是否显示演示GUI？[y/N]: ").strip().lower()
            if choice in ['y', 'yes', '是']:
                create_demo_gui()
        except KeyboardInterrupt:
            print("\n测试结束")
        
        print("\n🚀 可以开始打包:")
        print("   方法1: 运行 build_gui.bat")
        print("   方法2: 运行 python build_gui.py")
        
        return True
    else:
        print("\n⚠️  部分测试失败，请检查环境")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
