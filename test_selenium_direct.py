#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试使用Selenium直接请求多个API - 避免cookies时间窗口问题
"""

import json
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SeleniumDirectManager:
    def __init__(self, headless=True):
        self.driver = None
        self.headless = headless
        self.bypassed = False
        
    def setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
            
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36")
        
        # 禁用图片和CSS加载以提高速度
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.managed_default_content_settings.stylesheets": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            logging.info("✅ Chrome驱动创建成功")
            return True
        except Exception as e:
            logging.error(f"❌ Chrome驱动创建失败: {e}")
            return False
    
    def bypass_cloudflare_once(self, initial_url):
        """一次性绕过Cloudflare"""
        if self.bypassed:
            logging.info("已经绕过Cloudflare，跳过")
            return True
            
        logging.info(f"🔄 开始绕过Cloudflare: {initial_url}")
        
        try:
            # 访问初始URL
            self.driver.get(initial_url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 检查是否遇到Cloudflare挑战
            page_source = self.driver.page_source.lower()
            page_title = self.driver.title.lower()

            # 更全面的Cloudflare检测
            cf_indicators = [
                "just a moment",
                "checking your browser",
                "请稍候",
                "challenge-platform",
                "cdn-cgi/challenge"
            ]

            is_cf_challenge = any(indicator in page_source for indicator in cf_indicators) or \
                             any(indicator in page_title for indicator in cf_indicators)

            if is_cf_challenge:
                logging.info("检测到Cloudflare挑战，等待绕过...")

                # 等待挑战完成（最多60秒）
                for i in range(60):
                    time.sleep(1)
                    current_source = self.driver.page_source.lower()
                    current_title = self.driver.title.lower()

                    # 检查是否还在挑战中
                    still_challenging = any(indicator in current_source for indicator in cf_indicators) or \
                                       any(indicator in current_title for indicator in cf_indicators)

                    if not still_challenging:
                        logging.info(f"✅ Cloudflare挑战绕过成功 (耗时{i+1}秒)")
                        break

                    if i % 10 == 0:  # 每10秒报告一次进度
                        logging.info(f"等待Cloudflare挑战完成... ({i+1}/60秒)")
                else:
                    logging.error("❌ Cloudflare挑战绕过超时")
                    return False
            else:
                logging.info("✅ 没有遇到Cloudflare挑战")
            
            # 验证是否成功获取数据
            if self.is_json_response():
                logging.info("✅ 成功获取JSON数据，Cloudflare绕过完成")
                self.bypassed = True
                return True
            else:
                logging.warning("⚠️ 页面加载完成但未获取到JSON数据")
                return False
                
        except Exception as e:
            logging.error(f"❌ Cloudflare绕过失败: {e}")
            return False
    
    def is_json_response(self):
        """检查当前页面是否是JSON响应"""
        try:
            # 检查页面内容
            page_source = self.driver.page_source

            logging.info(f"页面标题: {self.driver.title}")
            logging.info(f"页面URL: {self.driver.current_url}")
            logging.info(f"页面内容长度: {len(page_source)}")

            # 方法1：查找<pre>标签中的JSON
            try:
                pre_element = self.driver.find_element(By.TAG_NAME, "pre")
                if pre_element:
                    pre_text = pre_element.text
                    logging.info(f"找到<pre>标签，内容长度: {len(pre_text)}")
                    if pre_text.strip():
                        data = json.loads(pre_text)
                        logging.info("✅ <pre>标签包含有效JSON")
                        return True
            except Exception as e:
                logging.debug(f"<pre>标签检查失败: {e}")

            # 方法2：检查页面是否包含JSON特征
            if '"data"' in page_source and ('"symbol"' in page_source or '"tokens"' in page_source):
                logging.info("✅ 页面包含JSON数据特征")
                return True

            # 方法3：检查content-type
            if "application/json" in page_source:
                logging.info("✅ 页面包含JSON content-type")
                return True

            # 保存页面用于调试
            with open('debug_page_check.html', 'w', encoding='utf-8') as f:
                f.write(page_source[:2000])
            logging.info("页面内容已保存到 debug_page_check.html")

            return False

        except Exception as e:
            logging.error(f"检查JSON响应失败: {e}")
            return False
    
    def get_json_data(self, url):
        """获取指定URL的JSON数据"""
        try:
            logging.info(f"📡 请求URL: {url}")
            
            # 导航到URL
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(2)
            
            # 检查是否遇到新的Cloudflare挑战
            page_source = self.driver.page_source.lower()
            page_title = self.driver.title.lower()

            cf_indicators = [
                "just a moment",
                "checking your browser",
                "请稍候",
                "challenge-platform",
                "cdn-cgi/challenge"
            ]

            is_cf_challenge = any(indicator in page_source for indicator in cf_indicators) or \
                             any(indicator in page_title for indicator in cf_indicators)

            if is_cf_challenge:
                logging.warning("⚠️ 遇到新的Cloudflare挑战，等待绕过...")

                # 等待挑战完成
                for i in range(30):
                    time.sleep(1)
                    current_source = self.driver.page_source.lower()
                    current_title = self.driver.title.lower()

                    still_challenging = any(indicator in current_source for indicator in cf_indicators) or \
                                       any(indicator in current_title for indicator in cf_indicators)

                    if not still_challenging:
                        logging.info(f"✅ 新的Cloudflare挑战绕过成功 (耗时{i+1}秒)")
                        break
                else:
                    logging.error("❌ 新的Cloudflare挑战绕过超时")
                    return None
            
            # 提取JSON数据
            page_source = self.driver.page_source
            
            # 方法1：从<pre>标签提取
            try:
                pre_element = self.driver.find_element(By.TAG_NAME, "pre")
                if pre_element:
                    json_text = pre_element.text
                    data = json.loads(json_text)
                    logging.info("✅ 从<pre>标签成功提取JSON数据")
                    return data
            except Exception as e:
                logging.debug(f"从<pre>标签提取失败: {e}")
            
            # 方法2：尝试解析整个页面
            try:
                data = json.loads(page_source)
                logging.info("✅ 直接解析页面为JSON成功")
                return data
            except Exception as e:
                logging.debug(f"直接解析页面失败: {e}")
            
            # 方法3：查找JSON模式
            import re
            json_pattern = r'\{.*"data".*\}'
            matches = re.findall(json_pattern, page_source, re.DOTALL)
            if matches:
                try:
                    data = json.loads(matches[0])
                    logging.info("✅ 通过正则表达式提取JSON成功")
                    return data
                except Exception as e:
                    logging.debug(f"正则表达式提取失败: {e}")
            
            logging.error("❌ 无法从页面提取JSON数据")
            
            # 保存页面用于调试
            with open(f'debug_page_{int(time.time())}.html', 'w', encoding='utf-8') as f:
                f.write(page_source[:5000])
            logging.info("页面内容已保存用于调试")
            
            return None
            
        except Exception as e:
            logging.error(f"❌ 获取JSON数据失败: {e}")
            return None
    
    def close(self):
        """关闭驱动"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Chrome驱动已关闭")

def test_selenium_direct():
    """测试Selenium直接请求方案"""
    print("🧪 测试Selenium直接请求方案")
    print("=" * 60)
    
    # 测试URLs
    test_urls = [
        "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10",
        "https://gmgn.ai/defi/quotation/v1/tokens/sol?limit=5",
    ]
    
    manager = SeleniumDirectManager(headless=False)  # 先用有头模式调试
    
    try:
        # 1. 设置驱动
        if not manager.setup_driver():
            print("❌ 驱动设置失败")
            return
        
        # 2. 绕过Cloudflare（使用第一个URL）
        print(f"\n🔄 使用第一个URL绕过Cloudflare...")
        if not manager.bypass_cloudflare_once(test_urls[0]):
            print("❌ Cloudflare绕过失败")
            return
        
        # 3. 测试多个API调用
        results = []
        for i, url in enumerate(test_urls, 1):
            print(f"\n📋 测试 {i}/{len(test_urls)}: {url}")
            print("-" * 40)
            
            start_time = time.time()
            data = manager.get_json_data(url)
            end_time = time.time()
            
            if data:
                # 分析数据
                data_count = 0
                if isinstance(data, dict):
                    if 'data' in data:
                        if isinstance(data['data'], list):
                            data_count = len(data['data'])
                        elif isinstance(data['data'], dict) and 'tokens' in data['data']:
                            data_count = len(data['data']['tokens'])
                        else:
                            data_count = 1
                
                print(f"✅ 成功获取数据，包含 {data_count} 条记录")
                print(f"⏱️  耗时: {end_time - start_time:.2f} 秒")
                
                results.append({
                    'url': url,
                    'success': True,
                    'data_count': data_count,
                    'time': end_time - start_time
                })
                
                # 显示第一条记录的信息
                if isinstance(data, dict) and 'data' in data and isinstance(data['data'], list) and len(data['data']) > 0:
                    first_item = data['data'][0]
                    if isinstance(first_item, dict):
                        symbol = first_item.get('symbol', first_item.get('name', 'Unknown'))
                        price = first_item.get('price', 'N/A')
                        print(f"第一条记录: {symbol}, 价格: {price}")
            else:
                print(f"❌ 获取失败")
                print(f"⏱️  耗时: {end_time - start_time:.2f} 秒")
                
                results.append({
                    'url': url,
                    'success': False,
                    'data_count': 0,
                    'time': end_time - start_time
                })
            
            # 短暂延迟
            time.sleep(1)
        
        # 4. 显示总结
        print(f"\n📊 测试总结:")
        print("-" * 40)
        success_count = sum(1 for r in results if r['success'])
        total_time = sum(r['time'] for r in results)
        
        print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"平均耗时: {total_time/len(results):.2f} 秒/请求")
        
        if success_count == len(results):
            print("\n🎉 所有API调用成功！Selenium直接请求方案可行")
        else:
            print(f"\n⚠️ 部分API调用失败，需要进一步优化")
            
    finally:
        manager.close()

if __name__ == "__main__":
    test_selenium_direct()
