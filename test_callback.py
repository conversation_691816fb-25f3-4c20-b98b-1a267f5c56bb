#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试能否接收Telegram命令
"""

from telegram.ext import Application, CommandHandler
import asyncio

BOT_TOKEN = "7616525518:AAGWbdc9y5pu9o9UVFkOEnhXN7Yk8TJVY-E"

async def test_command(update, context):
    import sys
    print(f"[收到命令] /test 来自用户: {update.effective_user.first_name}", flush=True)
    sys.stdout.flush()
    await update.message.reply_text("✅ 测试成功！机器人正在运行")

async def start_command(update, context):
    import sys
    print(f"[收到命令] /start 来自用户: {update.effective_user.first_name}", flush=True)
    sys.stdout.flush()
    await update.message.reply_text("🤖 机器人已启动！")

def main():
    print("🚀 启动简单测试机器人...")
    print("机器人用户名: @solzjf_bot")
    print("可以发送: /start, /test")
    print("按Ctrl+C退出")

    # 创建应用
    application = Application.builder().token(BOT_TOKEN).build()

    # 添加命令处理器
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("test", test_command))

    # 运行机器人
    try:
        application.run_polling()
    except KeyboardInterrupt:
        print("\n程序已退出")

if __name__ == "__main__":
    main()
