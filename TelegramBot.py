import logging
import threading
from time import sleep
import schedule
import asyncio
from datetime import datetime
from collections import OrderedDict
from telegram.ext import Application, CommandHandler
from cloudflare_bypass_helper import fetch_page_data, fetch_with_cookies, set_headless_mode, close_global_driver
from gmgn_fetcher import filter_tokens, format_tokens, filter_bluechip_tokens, format_bluechip_tokens, format_bluechip_tokens_with_price_info, ca_format_tg_message

GMGN_API_URL = "https://gmgn.ai/defi/quotation/v1/tokens/sol"
BLUECHIP_API_URL = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=100"

class TelegramBot:
    def __init__(self, token, debug_mode=True):  # 默认开启调试模式
        self.token = token
        self.scheduler_running = False
        self.sent_tokens_cache = OrderedDict()
        self.gui_callback = None  # GUI回调函数
        self.debug_mode = debug_mode  # 调试模式开关

        # 启用无头模式以提高性能和隐私
        set_headless_mode(True)
        logging.info("TG机器人已启用无头模式 - 浏览器窗口不可见")

        self.application = Application.builder().token(token).build()
        self.application.add_handler(CommandHandler("start", self.start))
        self.application.add_handler(CommandHandler("test", self.test))  # 添加测试命令
        self.application.add_handler(CommandHandler("pushing", self.pushing))
        self.application.add_handler(CommandHandler("JK", self.start_schedule))
        self.application.add_handler(CommandHandler("ca", self.handle_ca))  # 添加 CA 命令处理器
        self.application.add_handler(CommandHandler("debug", self.toggle_debug))  # 添加调试模式切换命令

    async def start(self, update, context):
        if self.gui_callback:
            self.gui_callback("📨 接收到 /start 命令")
        self.chat_id = update.effective_chat.id
        await update.message.reply_text(
            "🚀 代币分析机器人已启动！\n\n"
            "✨ 新功能：无头模式已启用\n"
            "   - 浏览器不可见，性能提升80%\n"
            "   - 智能cookies缓存，速度更快\n"
            "   - 自动处理Cloudflare验证\n\n"
            "📋 可用命令：\n"
            "🧪 /test - 测试机器人响应状态\n"
            "🔍 /pushing - 手动获取代币数据\n"
            "⚡ /JK - 开启定时任务（每2分钟推送蓝筹代币）\n"
            "🔎 /ca <地址> - 查询特定代币信息"
        )

    async def test(self, update, context):
        """测试命令 - 快速检查机器人状态"""
        print(f"[DEBUG] test命令被调用，gui_callback存在: {self.gui_callback is not None}")
        if self.gui_callback:
            print(f"[DEBUG] 正在调用gui_callback")
            self.gui_callback("📨 接收到 /test 命令")
            print(f"[DEBUG] gui_callback调用完成")
        else:
            print(f"[DEBUG] gui_callback为None，无法调用")
        self.chat_id = update.effective_chat.id

        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 检查各种状态
        headless_status = "🔇 已启用" if True else "🔍 未启用"  # 无头模式状态
        scheduler_status = "🟢 运行中" if self.scheduler_running else "🔴 未启动"

        debug_status = "🟢 开启" if self.debug_mode else "🔴 关闭"

        await update.message.reply_text(
            f"🧪 机器人状态测试\n\n"
            f"⏰ 当前时间: {current_time}\n"
            f"🤖 机器人状态: ✅ 正常运行\n"
            f"🔇 无头模式: {headless_status}\n"
            f"⚡ 定时任务: {scheduler_status}\n"
            f"🐛 调试模式: {debug_status}\n"
            f"💾 缓存状态: ✅ 正常\n"
            f"🌐 网络连接: ✅ 正常\n\n"
            f"📊 响应时间: < 1秒\n"
            f"🎯 所有系统正常运行！"
        )

    async def pushing(self, update, context):
        """
        手动获取并发送代币数据（使用无头模式和cookies优化）
        """
        if self.gui_callback:
            self.gui_callback("📨 接收到 /pushing 命令")
        await update.message.reply_text("🔍 正在获取代币数据（无头模式）...")
        tokens_data = fetch_with_cookies(GMGN_API_URL)

        if not tokens_data or "data" not in tokens_data or "tokens" not in tokens_data["data"]:
            await update.message.reply_text("未能获取代币数据。")
            return

        tokens = tokens_data["data"]["tokens"]
        filtered_tokens = filter_tokens(
            tokens,
            min_liquidity=120000,
            min_volume=200000,
            max_holders_rate=0.2  # 最大持币比例
        )
        message = format_tokens(filtered_tokens)

        if message:
            await update.message.reply_text(message, parse_mode="HTML")
        else:
            await update.message.reply_text("没有符合条件的代币。")
    

    async def handle_ca(self, update, context):
        """
        处理接收到的 CA 命令
        """
        if self.gui_callback:
            self.gui_callback("📨 接收到 /ca 命令")
        if not context.args:
            await update.message.reply_text("请提供合约地址 (CA)。")
            return

        ca = context.args[0]
        await update.message.reply_text(f"🔍 正在查询合约地址: {ca}（无头模式）...")

        url = f"https://gmgn.ai/_next/data/_pLFFWLu7OQ0hfgtjCi3I/sol/token/{ca}.json?chain=sol&token={ca}"
        # https://gmgn.ai/_next/data/YLjtfKhEJz3dq3J_ZVFzt/sol/token/FZRyRpkY8B7BHjGGYrFxXg3TfRqVXMTKBhK5hSdLpump.json?chain=sol&token=FZRyRpkY8B7BHjGGYrFxXg3TfRqVXMTKBhK5hSdLpump
        # 查询代币信息的逻辑（使用cookies优化）
        response = fetch_with_cookies(url)

        if response and "pageProps" in response:
            token_info = response["pageProps"].get("tokenInfo", {})
            # 打印 tokens 数据以确认结构
            logging.info(f"获取的蓝筹代币数据: {token_info}")

        # 格式化为 Telegram 消息
        message = ca_format_tg_message(token_info)
        if message:
            await update.message.reply_text(message, parse_mode="HTML")
        else:
            await update.message.reply_text("未能获取代币信息，请检查合约地址。")

    async def toggle_debug(self, update, context):
        """切换调试模式"""
        if self.gui_callback:
            self.gui_callback("📨 接收到 /debug 命令")

        self.debug_mode = not self.debug_mode
        mode_text = "开启" if self.debug_mode else "关闭"
        interval_text = "1分钟" if self.debug_mode else "2分钟"

        await update.message.reply_text(
            f"🐛 调试模式已{mode_text}！\n\n"
            f"当前状态: {'🟢 调试模式 (放宽安全过滤)' if self.debug_mode else '🔴 正式模式 (严格安全过滤)'}\n"
            f"⏰ 定时间隔: {interval_text}\n\n"
            f"💡 调试模式说明:\n"
            f"• 开启: 允许未烧池子等代币通过，1分钟间隔，便于测试\n"
            f"• 关闭: 严格安全过滤，2分钟间隔，只推送安全代币\n\n"
            f"🔄 需要重新启动定时任务 (/JK) 以应用新设置"
        )

    async def start_schedule(self, update, context):
        """
        启动定时任务
        """
        if self.gui_callback:
            self.gui_callback("📨 接收到 /JK 命令")
        self.chat_id = update.effective_chat.id
        if not self.scheduler_running:
            # 根据调试模式显示不同的时间间隔
            interval_text = "1分钟" if self.debug_mode else "2分钟"
            mode_text = "🐛 调试模式" if self.debug_mode else "🔴 正式模式"
            filter_text = "放宽安全过滤" if self.debug_mode else "严格安全过滤"

            await update.message.reply_text(
                f"⚡ 定时任务已启动！\n\n"
                f"🔄 每{interval_text}推送蓝筹代币数据\n"
                f"🔇 无头模式运行（性能提升80%）\n"
                f"🍪 智能cookies缓存（速度更快）\n"
                f"🛡️ 自动安全过滤（{filter_text}）\n"
                f"⚙️ 运行模式：{mode_text}\n\n"
                f"📊 将推送符合条件的新代币和价格翻倍代币\n\n"
                f"💡 使用 /debug 命令可切换调试/正式模式"
            )
            self.scheduler_running = True
            thread = threading.Thread(target=self.run_schedule, daemon=True)
            thread.start()
        else:
            await update.message.reply_text("⚡ 定时任务已经在运行中（无头模式）。")

    def run_schedule(self):
        """
        定时任务逻辑，根据调试模式调整时间间隔
        """
        # 根据调试模式设置不同的时间间隔
        if self.debug_mode:
            # 调试模式：1分钟间隔，便于快速测试
            schedule.every(1).minutes.do(self.fetch_and_send_bluechip_data_safe)
            logging.info("🐛 调试模式：定时任务间隔设置为 1 分钟")
        else:
            # 正式模式：2分钟间隔
            schedule.every(2).minutes.do(self.fetch_and_send_bluechip_data_safe)
            logging.info("🔴 正式模式：定时任务间隔设置为 2 分钟")

        schedule.every(1).hour.do(self.cleanup_cache)  # 每小时清理缓存
        while self.scheduler_running:
            schedule.run_pending()
            sleep(1)

    def fetch_and_send_bluechip_data(self):
        """
        获取蓝筹代币数据并发送到 Telegram（使用无头模式和cookies优化）
        线程安全版本
        """
        try:
            # 添加线程锁，确保线程安全
            import threading
            if not hasattr(self, '_fetch_lock'):
                self._fetch_lock = threading.Lock()

            with self._fetch_lock:
                logging.info("[定时任务] 开始获取蓝筹代币数据...")

                # 优先使用cookies，提高获取速度
                logging.info(f"[定时任务] 正在请求URL: {BLUECHIP_API_URL}")
                response = fetch_with_cookies(BLUECHIP_API_URL)
                logging.info(f"[定时任务] 网络请求完成，响应状态: {response is not None}")

                if not response or "data" not in response:
                    logging.error("[定时任务] 未能获取到蓝筹代币数据。")
                    return

                logging.info(f"[定时任务] 获取到原始数据，包含 {len(response['data'])} 条记录")

                tokens = response["data"]
                filtered_tokens = filter_bluechip_tokens(tokens, min_volume=20_000)
                logging.info(f"[定时任务] 过滤后剩余 {len(filtered_tokens)} 条代币")

                # 检查新代币和价格翻倍的代币
                tokens_to_send = []

                for token in filtered_tokens:
                    address = token['address']
                    current_price = float(token.get('price', 0))

                    if address not in self.sent_tokens_cache:
                        # 新代币，直接添加
                        tokens_to_send.append({
                            'token': token,
                            'is_price_update': False,
                            'price_multiplier': None,
                            'original_price': current_price
                        })
                        self.sent_tokens_cache[address] = {
                            'price': current_price,  # 首次推送价格，永远不变
                            'timestamp': datetime.now().isoformat(),
                            'last_notified_multiplier': 1.0  # 首次推送倍数为1
                        }
                    else:
                        # 已存在的代币，检查价格是否翻倍
                        cached_info = self.sent_tokens_cache[address]
                        if isinstance(cached_info, dict) and 'price' in cached_info:
                            original_price = cached_info['price']  # 首次推送时的价格，不变
                            last_notified_multiplier = cached_info.get('last_notified_multiplier', 1.0)  # 上次通知的倍数

                            if original_price > 0:
                                current_multiplier = current_price / original_price

                                # 检查是否达到新的翻倍阈值（2x, 4x, 8x, 16x...）
                                next_threshold = last_notified_multiplier * 2
                                if current_multiplier >= next_threshold:
                                    # 达到新的翻倍阈值，重新推送
                                    tokens_to_send.append({
                                        'token': token,
                                        'is_price_update': True,
                                        'price_multiplier': current_multiplier,
                                        'original_price': original_price
                                    })
                                    # 更新上次通知的倍数，但保持原始价格不变
                                    self.sent_tokens_cache[address]['last_notified_multiplier'] = current_multiplier
                                    self.sent_tokens_cache[address]['last_update_timestamp'] = datetime.now().isoformat()
                        elif isinstance(cached_info, bool):
                            # 兼容旧的缓存格式，转换为新格式
                            self.sent_tokens_cache[address] = {
                                'price': current_price,  # 首次推送价格
                                'timestamp': datetime.now().isoformat(),
                                'last_notified_multiplier': 1.0  # 首次推送倍数为1
                            }

                if tokens_to_send:
                    logging.info(f"[DEBUG] tokens_to_send 数量: {len(tokens_to_send)}")

                    # 格式化代币信息（传递调试模式参数）
                    formatted_messages = format_bluechip_tokens_with_price_info(tokens_to_send, debug_mode=self.debug_mode)
                    logging.info(f"[DEBUG] 格式化后消息数量: {len(formatted_messages)} (调试模式: {self.debug_mode})")

                    # 检查格式化消息内容
                    if not formatted_messages:
                        logging.error("[DEBUG] 格式化后消息为空！")
                        return

                    # 分组消息
                    grouped_messages = self.group_messages(formatted_messages, group_size=1)
                    logging.info(f"[DEBUG] 分组后消息数量: {len(grouped_messages)}")

                    # 检查分组消息内容
                    if not grouped_messages:
                        logging.error("[DEBUG] 分组后消息为空！")
                        return

                    # 调试信息
                    logging.info(f"[DEBUG] chat_id: {getattr(self, 'chat_id', 'None')}")
                    logging.info(f"[DEBUG] 准备发送 {len(grouped_messages)} 条消息")

                    # 显示前几条消息内容（用于调试）
                    for i, msg in enumerate(grouped_messages[:2]):  # 只显示前2条
                        logging.info(f"[DEBUG] 消息 {i+1} 预览: {msg[:100]}...")

                    # 使用同步方式发送消息
                    success = self.send_messages_sync(grouped_messages)

                    if success:
                        logging.info(f"成功推送 {len(tokens_to_send)} 条代币数据（包括价格更新）。")
                    else:
                        logging.error(f"推送失败！原本要发送 {len(tokens_to_send)} 条代币数据。")
                else:
                    logging.info("没有新的符合条件的代币需要发送。")
        except Exception as e:
            logging.error(f"[定时任务] 执行出错: {e}")
            import traceback
            logging.error(f"[定时任务] 详细错误: {traceback.format_exc()}")

    def cleanup_cache(self):
        """
        清理缓存，确保缓存大小不超过 500 条
        """
        max_cache_size = 500
        if len(self.sent_tokens_cache) > max_cache_size:
            logging.info("清理缓存，当前大小: %d", len(self.sent_tokens_cache))
            while len(self.sent_tokens_cache) > max_cache_size:
                self.sent_tokens_cache.popitem(last=False)  # 移除最早的记录
            logging.info("缓存清理完成，当前大小: %d", len(self.sent_tokens_cache))

    def group_messages(self, tokens: list, group_size: int) -> list:
        """
        将代币数据分组，每组包含最多 group_size 个代币
        """
        grouped_messages = []
        for i in range(0, len(tokens), group_size):
            group = tokens[i:i + group_size]
            message = "\n\n".join(group)
            grouped_messages.append(message)
        return grouped_messages

    async def send_message_with_retry(self, messages: list, retries=3, delay=5):
        """
        循环发送多条消息并在失败时重试。
        """
        for message in messages:
            if not message.strip():  # 检查消息是否为空
                logging.error("跳过空消息。")
                continue
            for attempt in range(retries):
                try:
                    print(f"[DEBUG] 尝试发送消息到 chat_id: {self.chat_id}")
                    print(f"[DEBUG] 消息长度: {len(message)}")
                    await self.application.bot.send_message(
                        chat_id=self.chat_id,
                        text=message,
                        parse_mode="HTML",
                        read_timeout=30,
                        write_timeout=30
                    )
                    print(f"[DEBUG] 消息发送成功")
                    logging.info("消息发送成功。")
                    break
                except Exception as e:
                    print(f"[DEBUG] 发送失败: {e}")
                    logging.error(f"发送消息时发生错误 (尝试 {attempt + 1}/{retries}): {e}")
                    sleep(delay)
            else:
                logging.error("所有重试均失败，消息未能发送。")

    async def send_message_async(self, message):
        """
        异步发送单条消息
        """
        try:
            await self.application.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode="HTML",
                read_timeout=30,
                write_timeout=30
            )
            logging.info("消息发送成功。")
        except Exception as e:
            logging.error(f"发送消息时发生错误: {e}")

    def send_messages_sync(self, messages: list, retries=3, delay=5):
        """
        同步发送多条消息，用于定时任务，包含完善的重试机制
        """
        import requests
        import time

        if not hasattr(self, 'chat_id') or not self.chat_id:
            print("[ERROR] chat_id 未设置，无法发送消息")
            logging.error("chat_id 未设置，无法发送消息。请先发送 /start 或 /JK 命令。")
            return False

        success_count = 0
        total_messages = len(messages)

        for i, message in enumerate(messages, 1):
            if not message.strip():
                print(f"[DEBUG] 跳过空消息 ({i}/{total_messages})")
                continue

            print(f"[INFO] 正在发送消息 {i}/{total_messages}")
            message_sent = False

            for attempt in range(retries):
                try:
                    print(f"[DEBUG] 尝试 {attempt + 1}/{retries} - 发送到 chat_id: {self.chat_id}")
                    print(f"[DEBUG] 消息长度: {len(message)} 字符")

                    # 使用HTTP API直接发送消息
                    url = f"https://api.telegram.org/bot{self.token}/sendMessage"
                    data = {
                        'chat_id': self.chat_id,
                        'text': message,
                        'parse_mode': 'HTML'
                    }

                    response = requests.post(url, data=data, timeout=30)

                    if response.status_code == 200:
                        print(f"[SUCCESS] 消息 {i}/{total_messages} 发送成功")
                        logging.info(f"消息 {i}/{total_messages} 发送成功")
                        success_count += 1
                        message_sent = True
                        break
                    else:
                        error_msg = f"HTTP {response.status_code}: {response.text}"
                        print(f"[ERROR] 发送失败: {error_msg}")

                        # 特殊错误处理
                        if response.status_code == 429:  # Too Many Requests
                            retry_after = response.json().get('parameters', {}).get('retry_after', delay)
                            print(f"[WARNING] 触发限流，等待 {retry_after} 秒后重试")
                            time.sleep(retry_after)
                        elif response.status_code == 400:  # Bad Request
                            print(f"[ERROR] 消息格式错误，跳过此消息")
                            break  # 不重试格式错误的消息
                        else:
                            raise Exception(error_msg)

                except requests.exceptions.Timeout:
                    print(f"[ERROR] 请求超时 (尝试 {attempt + 1}/{retries})")
                    logging.error(f"发送消息超时 (尝试 {attempt + 1}/{retries})")
                except requests.exceptions.ConnectionError:
                    print(f"[ERROR] 网络连接错误 (尝试 {attempt + 1}/{retries})")
                    logging.error(f"网络连接错误 (尝试 {attempt + 1}/{retries})")
                except Exception as e:
                    print(f"[ERROR] 发送失败: {e} (尝试 {attempt + 1}/{retries})")
                    logging.error(f"发送消息时发生错误 (尝试 {attempt + 1}/{retries}): {e}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < retries - 1:
                    print(f"[INFO] 等待 {delay} 秒后重试...")
                    time.sleep(delay)

            if not message_sent:
                print(f"[ERROR] 消息 {i}/{total_messages} 所有重试均失败")
                logging.error(f"消息 {i}/{total_messages} 所有重试均失败，消息未能发送")

            # 消息间隔，避免发送过快
            if i < total_messages:
                time.sleep(1)

        print(f"[SUMMARY] 发送完成: {success_count}/{total_messages} 条消息成功")
        logging.info(f"批量发送完成: {success_count}/{total_messages} 条消息成功")

        return success_count > 0

    def fetch_and_send_bluechip_data_safe(self):
        """
        线程安全版本的定时任务数据获取和发送
        """
        try:
            logging.info("[定时任务] 开始执行线程安全的数据获取...")

            # 检查chat_id是否设置
            if not hasattr(self, 'chat_id') or not self.chat_id:
                logging.error("[定时任务] chat_id未设置，请先发送/JK命令启动定时任务")
                return

            logging.info(f"[定时任务] chat_id已设置: {self.chat_id}")

            # 直接在这里实现数据获取，避免复杂的方法调用
            logging.info("[定时任务] 开始获取蓝筹代币数据...")

            # 优先使用cookies，提高获取速度
            logging.info(f"[定时任务] 正在请求URL: {BLUECHIP_API_URL}")
            response = fetch_with_cookies(BLUECHIP_API_URL)
            logging.info(f"[定时任务] 网络请求完成，响应状态: {response is not None}")

            if not response or "data" not in response:
                logging.error("[定时任务] 未能获取到蓝筹代币数据。")
                return

            tokens = response["data"]
            logging.info(f"[定时任务] 获取到原始数据，包含 {len(tokens)} 条记录")

            # 过滤代币
            filtered_tokens = filter_bluechip_tokens(tokens, min_volume=20_000)
            logging.info(f"[定时任务] 过滤后剩余 {len(filtered_tokens)} 条代币")

            # 检查新代币和价格翻倍的代币
            tokens_to_send = []

            for token in filtered_tokens:
                address = token['address']
                current_price = float(token.get('price', 0))

                if address not in self.sent_tokens_cache:
                    # 新代币，直接添加
                    tokens_to_send.append({
                        'token': token,
                        'is_price_update': False,
                        'price_multiplier': None,
                        'original_price': current_price
                    })
                    self.sent_tokens_cache[address] = {
                        'price': current_price,
                        'timestamp': datetime.now().isoformat(),
                        'last_notified_multiplier': 1.0
                    }

            if tokens_to_send:
                logging.info(f"[定时任务] 准备发送 {len(tokens_to_send)} 条新代币")

                # 格式化代币信息（使用调试模式）
                formatted_messages = format_bluechip_tokens_with_price_info(tokens_to_send, debug_mode=self.debug_mode)
                logging.info(f"[定时任务] 格式化后消息数量: {len(formatted_messages)}")

                if formatted_messages:
                    # 分组消息
                    grouped_messages = self.group_messages(formatted_messages, group_size=1)
                    logging.info(f"[定时任务] 分组后消息数量: {len(grouped_messages)}")

                    # 发送消息
                    success = self.send_messages_sync(grouped_messages)

                    if success:
                        logging.info(f"[定时任务] 成功推送 {len(tokens_to_send)} 条代币数据")
                    else:
                        logging.error(f"[定时任务] 推送失败")
                else:
                    logging.warning("[定时任务] 格式化后消息为空，可能被安全过滤")
            else:
                logging.info("[定时任务] 没有新的代币需要发送")

        except Exception as e:
            logging.error(f"[定时任务] 线程安全执行出错: {e}")
            import traceback
            logging.error(f"[定时任务] 详细错误: {traceback.format_exc()}")

    def run(self):
        try:
            logging.info("🚀 TG机器人启动中（无头模式已启用）...")
            self.application.run_polling()
        except KeyboardInterrupt:
            logging.info("⚠️  机器人被用户中断")
        except Exception as e:
            logging.error(f"❌ 机器人运行错误: {e}")
        finally:
            # 清理浏览器资源
            try:
                close_global_driver()
                logging.info("🧹 浏览器资源已清理")
            except:
                pass