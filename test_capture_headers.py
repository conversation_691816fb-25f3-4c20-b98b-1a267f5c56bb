#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
捕获 Selenium 成功请求时的完整请求头
"""

import json
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
import requests

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class HeaderCapture:
    def __init__(self):
        self.driver = None
        self.captured_headers = {}
        
    def setup_driver_with_logging(self):
        """设置带有网络日志的 Chrome 驱动"""
        chrome_options = Options()

        # 启用性能日志（新版本 Selenium 方式）
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        chrome_options.add_argument("--enable-network-service-logging")
        chrome_options.add_argument("--log-level=0")
        # chrome_options.add_argument("--headless")  # 先用有头模式调试

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            logging.info("✅ Chrome 驱动创建成功（带网络日志）")
            return True
        except Exception as e:
            logging.error(f"❌ Chrome 驱动创建失败: {e}")
            return False
    
    def bypass_and_capture(self, url):
        """绕过 Cloudflare 并捕获请求头"""
        logging.info(f"🔄 访问 URL 并捕获请求头: {url}")
        
        try:
            # 访问 URL
            self.driver.get(url)

            # 等待 Cloudflare 挑战完成
            max_wait = 60  # 最多等待60秒
            for i in range(max_wait):
                time.sleep(1)

                page_title = self.driver.title.lower()
                page_source = self.driver.page_source.lower()

                # 检查是否还在 Cloudflare 挑战中
                cf_indicators = [
                    "请稍候",
                    "just a moment",
                    "checking your browser",
                    "challenge-platform",
                    "cdn-cgi/challenge"
                ]

                still_challenging = any(indicator in page_title for indicator in cf_indicators) or \
                                   any(indicator in page_source for indicator in cf_indicators)

                if not still_challenging:
                    logging.info(f"✅ Cloudflare 挑战完成 (耗时 {i+1} 秒)")
                    break

                if i % 10 == 0:  # 每10秒报告一次
                    logging.info(f"等待 Cloudflare 挑战完成... ({i+1}/{max_wait}秒)")
            else:
                logging.error("❌ Cloudflare 挑战超时")
                return False

            # 获取最终状态
            page_title = self.driver.title
            page_source = self.driver.page_source

            logging.info(f"页面标题: {page_title}")
            logging.info(f"页面内容长度: {len(page_source)}")

            # 检查是否是 JSON 响应
            if self.is_json_page():
                logging.info("✅ 检测到 JSON 响应")
                
                # 捕获网络日志
                self.capture_network_logs(url)
                
                # 获取基本信息
                user_agent = self.driver.execute_script("return navigator.userAgent;")
                current_url = self.driver.current_url
                
                logging.info(f"User-Agent: {user_agent}")
                logging.info(f"当前 URL: {current_url}")
                
                return True
            else:
                logging.warning("❌ 未检测到 JSON 响应")
                return False
                
        except Exception as e:
            logging.error(f"❌ 访问失败: {e}")
            return False
    
    def is_json_page(self):
        """检查是否是 JSON 页面"""
        try:
            # 方法1：检查 <pre> 标签
            pre_elements = self.driver.find_elements("tag name", "pre")
            if pre_elements:
                pre_text = pre_elements[0].text
                if pre_text.strip().startswith('{') or pre_text.strip().startswith('['):
                    return True
            
            # 方法2：检查页面源码
            page_source = self.driver.page_source
            if '"data"' in page_source and ('"symbol"' in page_source or '"tokens"' in page_source):
                return True
                
            return False
        except:
            return False
    
    def capture_network_logs(self, target_url):
        """捕获网络日志中的请求头"""
        try:
            logs = self.driver.get_log('performance')
            
            for log in logs:
                message = json.loads(log['message'])
                
                if message['message']['method'] == 'Network.requestWillBeSent':
                    request = message['message']['params']['request']
                    url = request['url']
                    
                    # 找到目标 URL 的请求
                    if target_url in url:
                        headers = request['headers']
                        method = request['method']
                        
                        logging.info(f"🎯 找到目标请求: {method} {url}")
                        logging.info("📋 请求头:")
                        
                        self.captured_headers = headers
                        
                        for key, value in headers.items():
                            logging.info(f"  {key}: {value}")
                        
                        # 保存到文件
                        with open('captured_headers.json', 'w', encoding='utf-8') as f:
                            json.dump({
                                'url': url,
                                'method': method,
                                'headers': headers
                            }, f, indent=2, ensure_ascii=False)
                        
                        logging.info("✅ 请求头已保存到 captured_headers.json")
                        return True
            
            logging.warning("⚠️ 未找到目标 URL 的请求")
            return False
            
        except Exception as e:
            logging.error(f"❌ 捕获网络日志失败: {e}")
            return False
    
    def test_with_captured_headers(self, url):
        """使用捕获的请求头测试 requests"""
        if not self.captured_headers:
            logging.error("❌ 没有捕获到请求头")
            return False
        
        logging.info("🧪 使用捕获的请求头测试 requests...")
        
        # 获取 cookies
        selenium_cookies = self.driver.get_cookies()
        cookies = {}
        for cookie in selenium_cookies:
            cookies[cookie['name']] = cookie['value']
        
        logging.info(f"使用 cookies: {list(cookies.keys())}")
        
        try:
            # 使用捕获的请求头
            response = requests.get(url, headers=self.captured_headers, cookies=cookies, timeout=15)
            
            logging.info(f"响应状态码: {response.status_code}")
            logging.info(f"响应 Content-Type: {response.headers.get('content-type', 'unknown')}")
            
            if response.status_code == 200:
                # 检查是否被拦截
                if "Just a moment" in response.text or "Checking your browser" in response.text:
                    logging.warning("⚠️ 仍然被 Cloudflare 拦截")
                    return False
                
                # 尝试解析 JSON
                try:
                    if 'application/json' in response.headers.get('content-type', ''):
                        data = response.json()
                        logging.info("✅ requests 使用捕获的请求头成功获取 JSON 数据")
                        
                        if isinstance(data, dict) and 'data' in data:
                            count = len(data['data']) if isinstance(data['data'], list) else 1
                            logging.info(f"数据量: {count} 条记录")
                        
                        return True
                    else:
                        # HTML 响应
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(response.text, 'html.parser')
                        pre_content = soup.find('pre')
                        
                        if pre_content:
                            data = json.loads(pre_content.text)
                            logging.info("✅ requests 从 HTML 成功提取 JSON 数据")
                            return True
                        else:
                            logging.warning("❌ HTML 中未找到 JSON 数据")
                            return False
                            
                except json.JSONDecodeError as e:
                    logging.error(f"❌ JSON 解析失败: {e}")
                    return False
            else:
                logging.error(f"❌ HTTP 错误: {response.status_code}")
                
                # 保存错误响应
                with open('debug_requests_error.html', 'w', encoding='utf-8') as f:
                    f.write(response.text[:2000])
                logging.info("错误响应已保存到 debug_requests_error.html")
                
                return False
                
        except Exception as e:
            logging.error(f"❌ requests 请求失败: {e}")
            return False
    
    def close(self):
        """关闭驱动"""
        if self.driver:
            self.driver.quit()
            logging.info("🔒 Chrome 驱动已关闭")

def test_header_capture():
    """测试请求头捕获"""
    print("🧪 测试请求头捕获和复制")
    print("=" * 60)
    
    # 测试 URL
    test_url = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10"
    
    capture = HeaderCapture()
    
    try:
        # 1. 设置驱动
        if not capture.setup_driver_with_logging():
            print("❌ 驱动设置失败")
            return
        
        # 2. 绕过并捕获
        print(f"\n🔄 绕过 Cloudflare 并捕获请求头...")
        if not capture.bypass_and_capture(test_url):
            print("❌ 绕过或捕获失败")
            return
        
        # 3. 使用捕获的请求头测试
        print(f"\n🧪 使用捕获的请求头测试 requests...")
        success = capture.test_with_captured_headers(test_url)
        
        if success:
            print("🎉 成功！捕获的请求头有效")
            print("💡 可以将 captured_headers.json 中的请求头应用到主流程")
        else:
            print("❌ 失败！需要进一步分析")
            
        # 4. 显示捕获的请求头摘要
        if capture.captured_headers:
            print(f"\n📋 捕获的请求头摘要:")
            print("-" * 40)
            important_headers = ['user-agent', 'accept', 'accept-language', 'accept-encoding', 'referer', 'origin', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site']
            
            for header in important_headers:
                value = capture.captured_headers.get(header) or capture.captured_headers.get(header.title()) or capture.captured_headers.get(header.upper())
                if value:
                    print(f"  {header}: {value[:100]}...")
        
    finally:
        capture.close()

if __name__ == "__main__":
    test_header_capture()
