#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化的 Selenium 批量请求方案
一次绕过，同一会话中完成多个API调用
"""

import json
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SeleniumBatchManager:
    def __init__(self, headless=True):
        self.driver = None
        self.headless = headless
        self.bypassed = False
        self.session_start_time = None
        
    def setup_driver(self):
        """设置优化的Chrome驱动"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument("--headless")
            logging.info("🔇 启用无头模式")
        else:
            logging.info("👁️ 启用有头模式（调试）")
            
        # 性能优化参数
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")  # 禁用图片加载
        chrome_options.add_argument("--disable-javascript")  # 禁用JS（在获取数据后）
        chrome_options.add_argument("--window-size=1920,1080")
        
        # 设置User-Agent
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        
        # 禁用不必要的功能以提高速度
        prefs = {
            "profile.managed_default_content_settings.images": 2,  # 禁用图片
            "profile.managed_default_content_settings.stylesheets": 2,  # 禁用CSS
            "profile.managed_default_content_settings.plugins": 2,  # 禁用插件
            "profile.managed_default_content_settings.popups": 2,  # 禁用弹窗
            "profile.managed_default_content_settings.geolocation": 2,  # 禁用地理位置
            "profile.managed_default_content_settings.notifications": 2,  # 禁用通知
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.session_start_time = datetime.now()
            logging.info("✅ Chrome驱动创建成功")
            return True
        except Exception as e:
            logging.error(f"❌ Chrome驱动创建失败: {e}")
            return False
    
    def bypass_cloudflare_once(self, initial_url):
        """一次性绕过Cloudflare"""
        if self.bypassed:
            logging.info("✅ 已经绕过Cloudflare，跳过")
            return True
            
        logging.info(f"🔄 开始绕过Cloudflare: {initial_url}")
        bypass_start = time.time()
        
        try:
            # 访问初始URL
            self.driver.get(initial_url)
            
            # 等待Cloudflare挑战完成
            max_wait = 60
            for i in range(max_wait):
                time.sleep(1)
                
                page_title = self.driver.title.lower()
                page_source = self.driver.page_source.lower()
                
                # 检查Cloudflare挑战指标
                cf_indicators = [
                    "请稍候", "just a moment", "checking your browser",
                    "challenge-platform", "cdn-cgi/challenge"
                ]
                
                still_challenging = any(indicator in page_title for indicator in cf_indicators) or \
                                   any(indicator in page_source for indicator in cf_indicators)
                
                if not still_challenging:
                    bypass_time = time.time() - bypass_start
                    logging.info(f"✅ Cloudflare挑战绕过成功 (耗时 {bypass_time:.2f} 秒)")
                    break
                    
                if i % 10 == 0:
                    logging.info(f"等待Cloudflare挑战完成... ({i+1}/{max_wait}秒)")
            else:
                logging.error("❌ Cloudflare挑战绕过超时")
                return False
            
            # 验证是否成功获取数据
            if self.extract_json_data():
                self.bypassed = True
                logging.info("✅ Cloudflare绕过完成，会话已建立")
                return True
            else:
                logging.warning("⚠️ 绕过完成但未获取到数据")
                return False
                
        except Exception as e:
            logging.error(f"❌ Cloudflare绕过失败: {e}")
            return False
    
    def navigate_to_url(self, url):
        """在同一会话中导航到新URL，处理可能的新挑战"""
        try:
            nav_start = time.time()
            logging.info(f"📡 导航到: {url}")

            self.driver.get(url)

            # 等待页面加载
            time.sleep(3)

            # 检查是否遇到新的Cloudflare挑战
            page_source = self.driver.page_source.lower()
            page_title = self.driver.title.lower()

            cf_indicators = [
                "请稍候", "just a moment", "checking your browser",
                "challenge-platform", "cdn-cgi/challenge"
            ]

            is_cf_challenge = any(indicator in page_source for indicator in cf_indicators) or \
                             any(indicator in page_title for indicator in cf_indicators)

            if is_cf_challenge:
                logging.warning("⚠️ 遇到新的Cloudflare挑战，等待绕过...")

                # 等待新挑战完成
                for i in range(30):  # 最多等待30秒
                    time.sleep(1)
                    current_source = self.driver.page_source.lower()
                    current_title = self.driver.title.lower()

                    still_challenging = any(indicator in current_source for indicator in cf_indicators) or \
                                       any(indicator in current_title for indicator in cf_indicators)

                    if not still_challenging:
                        logging.info(f"✅ 新的Cloudflare挑战绕过成功 (耗时 {i+1} 秒)")
                        break

                    if i % 5 == 0:  # 每5秒报告一次
                        logging.info(f"等待新挑战完成... ({i+1}/30秒)")
                else:
                    logging.error("❌ 新的Cloudflare挑战绕过超时")
                    return None

            nav_time = time.time() - nav_start
            logging.info(f"✅ 导航完成 (总耗时 {nav_time:.2f} 秒)")

            return self.extract_json_data()

        except Exception as e:
            logging.error(f"❌ 导航失败: {e}")
            return None
    
    def extract_json_data(self):
        """从当前页面提取JSON数据"""
        try:
            page_source = self.driver.page_source
            
            # 方法1：从<pre>标签提取
            try:
                pre_element = self.driver.find_element(By.TAG_NAME, "pre")
                if pre_element:
                    json_text = pre_element.text
                    if json_text.strip():
                        data = json.loads(json_text)
                        logging.debug("✅ 从<pre>标签提取JSON成功")
                        return data
            except Exception as e:
                logging.debug(f"<pre>标签提取失败: {e}")
            
            # 方法2：尝试解析整个页面
            try:
                data = json.loads(page_source)
                logging.debug("✅ 直接解析页面为JSON成功")
                return data
            except Exception as e:
                logging.debug(f"直接解析失败: {e}")
            
            # 方法3：正则表达式查找JSON
            import re
            json_pattern = r'\{.*?"data".*?\}'
            matches = re.findall(json_pattern, page_source, re.DOTALL)
            if matches:
                try:
                    data = json.loads(matches[0])
                    logging.debug("✅ 正则表达式提取JSON成功")
                    return data
                except Exception as e:
                    logging.debug(f"正则表达式提取失败: {e}")
            
            logging.warning("❌ 无法从页面提取JSON数据")
            return None
            
        except Exception as e:
            logging.error(f"❌ 提取JSON数据失败: {e}")
            return None
    
    def batch_request(self, urls):
        """批量请求多个URL"""
        if not self.bypassed:
            logging.error("❌ 尚未绕过Cloudflare，无法进行批量请求")
            return []
        
        results = []
        total_start = time.time()
        
        for i, url in enumerate(urls, 1):
            logging.info(f"📋 批量请求 {i}/{len(urls)}")
            
            request_start = time.time()
            data = self.navigate_to_url(url)
            request_time = time.time() - request_start
            
            if data:
                # 分析数据
                data_count = 0
                if isinstance(data, dict) and 'data' in data:
                    if isinstance(data['data'], list):
                        data_count = len(data['data'])
                    else:
                        data_count = 1
                
                logging.info(f"✅ 成功获取 {data_count} 条记录 (耗时 {request_time:.2f} 秒)")
                
                results.append({
                    'url': url,
                    'success': True,
                    'data': data,
                    'data_count': data_count,
                    'time': request_time
                })
                
                # 显示第一条记录信息
                if isinstance(data, dict) and 'data' in data and isinstance(data['data'], list) and len(data['data']) > 0:
                    first_item = data['data'][0]
                    if isinstance(first_item, dict):
                        symbol = first_item.get('symbol', first_item.get('name', 'Unknown'))
                        price = first_item.get('price', 'N/A')
                        logging.info(f"第一条记录: {symbol}, 价格: {price}")
            else:
                logging.error(f"❌ 获取失败 (耗时 {request_time:.2f} 秒)")
                results.append({
                    'url': url,
                    'success': False,
                    'data': None,
                    'data_count': 0,
                    'time': request_time
                })
            
            # 请求间短暂延迟
            time.sleep(0.5)
        
        total_time = time.time() - total_start
        logging.info(f"📊 批量请求完成，总耗时: {total_time:.2f} 秒")
        
        return results
    
    def get_session_info(self):
        """获取会话信息"""
        if self.session_start_time:
            session_duration = (datetime.now() - self.session_start_time).total_seconds()
            return {
                'session_duration': session_duration,
                'bypassed': self.bypassed,
                'driver_active': self.driver is not None
            }
        return None
    
    def close(self):
        """关闭驱动"""
        if self.driver:
            self.driver.quit()
            session_info = self.get_session_info()
            if session_info:
                logging.info(f"🔒 会话结束，持续时间: {session_info['session_duration']:.2f} 秒")
            else:
                logging.info("🔒 Chrome驱动已关闭")

def test_selenium_batch():
    """测试Selenium批量请求方案"""
    print("🧪 测试优化的Selenium批量请求方案")
    print("=" * 60)
    
    # 测试URLs
    test_urls = [
        "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10",
        "https://gmgn.ai/defi/quotation/v1/tokens/sol?limit=5",
        "https://gmgn.ai/api/v1/bluechip_rank/sol?limit=3",
    ]
    
    manager = SeleniumBatchManager(headless=False)  # 先用有头模式调试
    
    try:
        # 1. 设置驱动
        print("\n🔧 设置Chrome驱动...")
        if not manager.setup_driver():
            print("❌ 驱动设置失败")
            return False
        
        # 2. 一次性绕过Cloudflare
        print(f"\n🔄 一次性绕过Cloudflare...")
        if not manager.bypass_cloudflare_once(test_urls[0]):
            print("❌ Cloudflare绕过失败")
            return False
        
        # 3. 批量请求所有URL
        print(f"\n📋 开始批量请求 {len(test_urls)} 个API...")
        results = manager.batch_request(test_urls)
        
        # 4. 分析结果
        print(f"\n📊 批量请求结果分析:")
        print("=" * 40)
        
        success_count = sum(1 for r in results if r['success'])
        total_data = sum(r['data_count'] for r in results if r['success'])
        total_time = sum(r['time'] for r in results)
        avg_time = total_time / len(results) if results else 0
        
        print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        print(f"总数据量: {total_data} 条记录")
        print(f"总请求时间: {total_time:.2f} 秒")
        print(f"平均请求时间: {avg_time:.2f} 秒/请求")
        
        # 会话信息
        session_info = manager.get_session_info()
        if session_info:
            print(f"会话持续时间: {session_info['session_duration']:.2f} 秒")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for i, result in enumerate(results, 1):
            status = "✅ 成功" if result['success'] else "❌ 失败"
            url_short = result['url'].split('?')[0].split('/')[-1]
            print(f"  {i}. {url_short}: {status} ({result['time']:.2f}s, {result['data_count']}条)")
        
        # 性能评估
        print(f"\n⚡ 性能评估:")
        if success_count == len(results):
            print("🎉 完美！所有请求都成功")
            print("💡 优势：")
            print("  - 一次绕过，多次使用")
            print("  - 同一会话，无需重复验证")
            print("  - 平均请求时间短")
            
            if avg_time < 3:
                print("  - 响应速度优秀 (<3秒)")
            elif avg_time < 5:
                print("  - 响应速度良好 (<5秒)")
            else:
                print("  - 响应速度可接受")
                
            return True
        else:
            print("⚠️ 部分请求失败，需要优化")
            return False
            
    finally:
        manager.close()

def test_session_reuse():
    """测试会话复用性能"""
    print(f"\n🔄 测试会话复用性能...")
    print("-" * 40)
    
    manager = SeleniumBatchManager(headless=True)
    
    try:
        if not manager.setup_driver():
            return
        
        # 绕过一次
        url = "https://gmgn.ai/api/v1/bluechip_rank/sol?limit=3"
        if not manager.bypass_cloudflare_once(url):
            return
        
        # 连续请求同一URL多次，测试会话稳定性
        print("连续请求测试:")
        for i in range(5):
            start_time = time.time()
            data = manager.navigate_to_url(url)
            end_time = time.time()
            
            if data:
                count = len(data.get('data', [])) if isinstance(data, dict) else 0
                print(f"  请求 {i+1}: ✅ 成功 ({end_time - start_time:.2f}秒, {count}条)")
            else:
                print(f"  请求 {i+1}: ❌ 失败 ({end_time - start_time:.2f}秒)")
            
            time.sleep(1)
            
    finally:
        manager.close()

if __name__ == "__main__":
    # 主要测试
    success = test_selenium_batch()
    
    # 会话复用测试
    if success:
        test_session_reuse()
    
    print(f"\n🏁 测试完成！")
