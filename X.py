import tweepy

# 替换为你的 API 密钥和访问令牌
API_KEY = 'iQTkZ5E5fS6WCQJxZxnMibapZ'
API_SECRET_KEY = 'XyHiKzXVOfJXDleceo0hJGZPtueM4ng7O3fJXBagxFDdKcZkFA'
ACCESS_TOKEN = '1580506583984791552-Dv4mDXLIBWISCBxoSbVcF0sIlKkuR3'
ACCESS_TOKEN_SECRET = 'g15P6NxWfNd1G79vbtbdejSUELON32sraIIw6WfcRsWgw'
BearerToken = 'AAAAAAAAAAAAAAAAAAAAABPFxQEAAAAAp5IIJcBcX8VWxlYrMR9ryZjrPwI%3DsH2HOy5v0q0zZinvrhaFofO2bDQZRProUrtU1ufKbgjgkbdSYI'


# 认证
client = tweepy.Client(bearer_token=BearerToken)

# 替换为你要查询的推特用户名
username = 'MELANIATRUMP'

try:
    user = client.get_user(username=username)
  # 打印整个 user 对象
    print(user)
    print(f"用户名: {user.data.username}")
    print(f"关注者数量: {user.data.public_metrics['followers_count']}")
    print(f"正在关注的用户数量: {user.data.public_metrics['following_count']}")
    print(f"推文数量: {user.data.public_metrics['tweet_count']}")
except tweepy.TweepyException as e:
    print(f"发生错误: {e}")

