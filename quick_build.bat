@echo off
chcp 65001 >nul
echo 🚀 快速打包工具
echo ==================

echo.
echo 📦 正在安装依赖...
pip install lxml openpyxl DataRecorder

echo.
echo 🔧 转换图标...
python -c "
from PIL import Image
import os
try:
    img = Image.open('img/狗头.jpg')
    img.save('img/icon.ico', format='ICO', sizes=[(16,16), (32,32), (48,48), (64,64)])
    print('✅ 图标转换成功')
except Exception as e:
    print(f'❌ 图标转换失败: {e}')
"

echo.
echo 🚀 开始打包...
pyinstaller ^
    --onefile ^
    --windowed ^
    --icon=img/icon.ico ^
    --name=SolanaTokenBot ^
    --add-data="img;img" ^
    --add-data="cloudflare_bypass_helper.py;." ^
    --add-data="gmgn_fetcher.py;." ^
    --add-data="TelegramBot.py;." ^
    --add-data="CloudflareBypasser.py;." ^
    --hidden-import=telegram ^
    --hidden-import=telegram.ext ^
    --hidden-import=DrissionPage ^
    --hidden-import=bs4 ^
    --hidden-import=requests ^
    --hidden-import=PIL ^
    --hidden-import=PIL.Image ^
    --hidden-import=PIL.ImageTk ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.scrolledtext ^
    --hidden-import=openpyxl ^
    --hidden-import=DataRecorder ^
    --hidden-import=lxml ^
    --hidden-import=lxml.etree ^
    --hidden-import=lxml.html ^
    --hidden-import=schedule ^
    --hidden-import=asyncio ^
    --hidden-import=websockets ^
    --hidden-import=aiohttp ^
    --exclude-module=matplotlib ^
    --exclude-module=numpy ^
    --exclude-module=pandas ^
    --exclude-module=scipy ^
    --clean ^
    main_gui.py

echo.
if exist "dist\SolanaTokenBot.exe" (
    echo ✅ 打包成功！
    echo 📁 文件位置: dist\SolanaTokenBot.exe
    echo.
    echo 🧪 是否测试运行？[Y/N]
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 🚀 启动程序...
        start "" "dist\SolanaTokenBot.exe"
    )
) else (
    echo ❌ 打包失败！
    echo 请检查错误信息
)

echo.
pause
