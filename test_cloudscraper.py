#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 CloudScraper - 快速绕过 Cloudflare 5秒盾
"""

import cloudscraper
import json
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_cloudscraper():
    """测试 CloudScraper 绕过 Cloudflare"""
    print("🧪 测试 CloudScraper 快速绕过方案")
    print("=" * 60)
    
    # 测试URLs
    test_urls = [
        "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10",
        "https://gmgn.ai/defi/quotation/v1/tokens/sol?limit=5",
    ]
    
    # 创建 CloudScraper 实例
    print("🔧 创建 CloudScraper 实例...")
    scraper = cloudscraper.create_scraper(
        browser={
            'browser': 'chrome',
            'platform': 'windows',
            'desktop': True
        }
    )
    
    print("✅ CloudScraper 创建成功")
    
    # 测试多个API调用
    results = []
    total_start_time = time.time()
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n📋 测试 {i}/{len(test_urls)}: {url}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            logging.info(f"发送请求到: {url}")
            
            # 使用 CloudScraper 发送请求
            response = scraper.get(url, timeout=30)
            
            end_time = time.time()
            request_time = end_time - start_time
            
            logging.info(f"响应状态码: {response.status_code}")
            logging.info(f"响应Content-Type: {response.headers.get('content-type', 'unknown')}")
            logging.info(f"响应长度: {len(response.content)} bytes")
            
            if response.status_code == 200:
                # 检查是否被Cloudflare拦截
                if "Just a moment" in response.text or "Checking your browser" in response.text:
                    print(f"⚠️ 仍然被Cloudflare拦截")
                    results.append({
                        'url': url,
                        'success': False,
                        'error': 'Cloudflare challenge',
                        'time': request_time
                    })
                    continue
                
                # 尝试解析JSON
                try:
                    content_type = response.headers.get('content-type', '')
                    
                    if 'application/json' in content_type:
                        # 直接JSON响应
                        data = response.json()
                        data_count = 0
                        
                        if isinstance(data, dict) and 'data' in data:
                            if isinstance(data['data'], list):
                                data_count = len(data['data'])
                            elif isinstance(data['data'], dict) and 'tokens' in data['data']:
                                data_count = len(data['data']['tokens'])
                            else:
                                data_count = 1
                        
                        print(f"✅ 成功获取JSON数据，包含 {data_count} 条记录")
                        print(f"⏱️  耗时: {request_time:.2f} 秒")
                        
                        # 显示第一条记录的信息
                        if isinstance(data, dict) and 'data' in data and isinstance(data['data'], list) and len(data['data']) > 0:
                            first_item = data['data'][0]
                            if isinstance(first_item, dict):
                                symbol = first_item.get('symbol', first_item.get('name', 'Unknown'))
                                price = first_item.get('price', 'N/A')
                                volume = first_item.get('volume', 'N/A')
                                print(f"第一条记录: {symbol}, 价格: {price}, 交易量: {volume}")
                        
                        results.append({
                            'url': url,
                            'success': True,
                            'data_count': data_count,
                            'time': request_time
                        })
                        
                    else:
                        # HTML响应，尝试从<pre>标签提取JSON
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(response.text, 'html.parser')
                        pre_content = soup.find('pre')
                        
                        if pre_content and pre_content.text.strip():
                            data = json.loads(pre_content.text)
                            data_count = 0
                            
                            if isinstance(data, dict) and 'data' in data:
                                if isinstance(data['data'], list):
                                    data_count = len(data['data'])
                                else:
                                    data_count = 1
                            
                            print(f"✅ 从HTML提取JSON数据，包含 {data_count} 条记录")
                            print(f"⏱️  耗时: {request_time:.2f} 秒")
                            
                            results.append({
                                'url': url,
                                'success': True,
                                'data_count': data_count,
                                'time': request_time
                            })
                        else:
                            print(f"❌ HTML中未找到JSON数据")
                            # 保存HTML用于调试
                            with open(f'debug_cloudscraper_{i}.html', 'w', encoding='utf-8') as f:
                                f.write(response.text[:2000])
                            print(f"响应已保存到 debug_cloudscraper_{i}.html")
                            
                            results.append({
                                'url': url,
                                'success': False,
                                'error': 'No JSON in HTML',
                                'time': request_time
                            })
                            
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    results.append({
                        'url': url,
                        'success': False,
                        'error': f'JSON decode error: {e}',
                        'time': request_time
                    })
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
                # 保存错误响应
                with open(f'debug_cloudscraper_error_{i}.html', 'w', encoding='utf-8') as f:
                    f.write(response.text[:1000])
                print(f"错误响应已保存到 debug_cloudscraper_error_{i}.html")
                
                results.append({
                    'url': url,
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'time': request_time
                })
                
        except Exception as e:
            end_time = time.time()
            request_time = end_time - start_time
            
            print(f"❌ 请求异常: {e}")
            print(f"⏱️  耗时: {request_time:.2f} 秒")
            
            results.append({
                'url': url,
                'success': False,
                'error': str(e),
                'time': request_time
            })
        
        # 短暂延迟
        time.sleep(1)
    
    total_end_time = time.time()
    total_time = total_end_time - total_start_time
    
    # 显示总结
    print(f"\n📊 CloudScraper 测试总结:")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    total_data = sum(r.get('data_count', 0) for r in results if r['success'])
    avg_time = sum(r['time'] for r in results) / len(results) if results else 0
    
    print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    print(f"总耗时: {total_time:.2f} 秒")
    print(f"平均耗时: {avg_time:.2f} 秒/请求")
    print(f"总数据量: {total_data} 条记录")
    
    # 显示失败原因
    if success_count < len(results):
        print(f"\n❌ 失败原因:")
        for i, result in enumerate(results, 1):
            if not result['success']:
                print(f"  {i}. {result.get('error', 'Unknown error')}")
    
    # 性能对比
    print(f"\n⚡ 性能分析:")
    print(f"  CloudScraper 平均响应时间: {avg_time:.2f} 秒")
    print(f"  相比 Selenium 方案的优势:")
    print(f"    - 无需启动浏览器实例")
    print(f"    - 内存占用更少")
    print(f"    - 启动时间更快")
    
    if success_count == len(results):
        print(f"\n🎉 CloudScraper 完全成功！建议采用此方案")
        return True
    elif success_count > 0:
        print(f"\n⚠️ CloudScraper 部分成功，可能需要优化配置")
        return False
    else:
        print(f"\n❌ CloudScraper 完全失败，可能需要回退到 Selenium 方案")
        return False

def test_cloudscraper_session_reuse():
    """测试 CloudScraper 会话复用"""
    print(f"\n🔄 测试 CloudScraper 会话复用...")
    print("-" * 40)
    
    scraper = cloudscraper.create_scraper()
    
    # 连续请求同一个URL多次
    url = "https://gmgn.ai/api/v1/bluechip_rank/sol?limit=3"
    
    for i in range(3):
        start_time = time.time()
        try:
            response = scraper.get(url, timeout=15)
            end_time = time.time()
            
            if response.status_code == 200:
                print(f"请求 {i+1}: ✅ 成功 ({end_time - start_time:.2f}秒)")
            else:
                print(f"请求 {i+1}: ❌ HTTP {response.status_code}")
                
        except Exception as e:
            end_time = time.time()
            print(f"请求 {i+1}: ❌ 异常 {e} ({end_time - start_time:.2f}秒)")
        
        time.sleep(0.5)

if __name__ == "__main__":
    # 主要测试
    success = test_cloudscraper()
    
    # 会话复用测试
    if success:
        test_cloudscraper_session_reuse()
    
    print(f"\n🏁 测试完成！")
