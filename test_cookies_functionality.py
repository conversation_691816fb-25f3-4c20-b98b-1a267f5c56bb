#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cloudflare绕过和Cookies功能
"""

import logging
import time
import os
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_imports():
    """测试导入功能"""
    print("=== 测试导入功能 ===")
    try:
        from cloudflare_bypass_helper import (
            fetch_page_data, 
            fetch_with_cookies, 
            close_global_driver,
            load_cookies_from_file,
            get_domain_from_url,
            save_cookies_to_file
        )
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functions():
    """测试基础函数"""
    print("\n=== 测试基础函数 ===")
    
    try:
        from cloudflare_bypass_helper import get_domain_from_url
        
        # 测试域名提取
        test_url = "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens"
        domain = get_domain_from_url(test_url)
        print(f"✅ 域名提取测试: {domain}")
        
        # 测试cookies保存和加载
        from cloudflare_bypass_helper import save_cookies_to_file, load_cookies_from_file
        
        test_cookies = {"test_cookie": "test_value", "session_id": "12345"}
        save_cookies_to_file(test_cookies, "test.com")
        loaded_cookies = load_cookies_from_file("test.com")
        
        if loaded_cookies == test_cookies:
            print("✅ Cookies保存和加载测试通过")
        else:
            print(f"❌ Cookies测试失败: {loaded_cookies} != {test_cookies}")
            
        return True
    except Exception as e:
        print(f"❌ 基础函数测试失败: {e}")
        return False

def test_browser_creation():
    """测试浏览器创建（不实际访问网站）"""
    print("\n=== 测试浏览器创建 ===")
    
    try:
        from cloudflare_bypass_helper import get_or_create_driver, close_global_driver
        
        print("正在创建浏览器实例...")
        driver = get_or_create_driver()
        
        if driver:
            print("✅ 浏览器实例创建成功")
            print(f"   浏览器类型: {type(driver)}")
            
            # 测试简单页面访问
            try:
                driver.get("about:blank")
                print("✅ 浏览器可以正常导航")
            except Exception as e:
                print(f"⚠️  浏览器导航测试失败: {e}")
            
            # 关闭浏览器
            close_global_driver()
            print("✅ 浏览器实例已关闭")
            return True
        else:
            print("❌ 浏览器实例创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 浏览器创建测试失败: {e}")
        try:
            from cloudflare_bypass_helper import close_global_driver
            close_global_driver()
        except:
            pass
        return False

def test_requests_functionality():
    """测试requests功能（不需要绕过Cloudflare）"""
    print("\n=== 测试Requests功能 ===")
    
    try:
        import requests
        from bs4 import BeautifulSoup
        
        # 测试一个简单的HTTP请求
        test_url = "https://httpbin.org/json"
        response = requests.get(test_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ HTTP请求功能正常")
            data = response.json()
            print(f"   响应数据类型: {type(data)}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
        # 测试BeautifulSoup解析
        html_content = "<html><body><pre>{'test': 'data'}</pre></body></html>"
        soup = BeautifulSoup(html_content, 'html.parser')
        pre_content = soup.find('pre')
        
        if pre_content:
            print("✅ BeautifulSoup解析功能正常")
        else:
            print("❌ BeautifulSoup解析失败")
            
        return True
    except Exception as e:
        print(f"❌ Requests功能测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n=== 测试文件操作 ===")
    
    try:
        import pickle
        import json
        
        # 测试pickle文件操作
        test_data = {"cookies": {"session": "test123"}, "domain": "test.com"}
        
        with open("test_cookies.pkl", "wb") as f:
            pickle.dump(test_data, f)
        
        with open("test_cookies.pkl", "rb") as f:
            loaded_data = pickle.load(f)
        
        if loaded_data == test_data:
            print("✅ Pickle文件操作正常")
        else:
            print("❌ Pickle文件操作失败")
        
        # 测试JSON文件操作
        json_data = {"test": "data", "number": 123}
        with open("test_data.json", "w", encoding="utf-8") as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        with open("test_data.json", "r", encoding="utf-8") as f:
            loaded_json = json.load(f)
        
        if loaded_json == json_data:
            print("✅ JSON文件操作正常")
        else:
            print("❌ JSON文件操作失败")
        
        # 清理测试文件
        try:
            os.remove("test_cookies.pkl")
            os.remove("test_data.json")
            print("✅ 测试文件已清理")
        except:
            pass
            
        return True
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Cloudflare绕过和Cookies功能\n")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("导入功能", test_imports()))
    test_results.append(("基础函数", test_basic_functions()))
    test_results.append(("文件操作", test_file_operations()))
    test_results.append(("Requests功能", test_requests_functionality()))
    test_results.append(("浏览器创建", test_browser_creation()))
    
    # 汇总测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-"*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期的错误: {e}")
        logging.error("Unexpected error during testing", exc_info=True)
        sys.exit(1)
