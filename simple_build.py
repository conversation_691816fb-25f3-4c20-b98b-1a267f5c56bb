#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版打包脚本 - 避免NumPy兼容性问题
"""

import os
import sys
import subprocess
from PIL import Image

def convert_icon():
    """转换图标"""
    try:
        jpg_path = os.path.join("img", "狗头.jpg")
        ico_path = os.path.join("img", "icon.ico")
        
        if os.path.exists(jpg_path):
            img = Image.open(jpg_path)
            icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64)]
            img.save(ico_path, format='ICO', sizes=icon_sizes)
            print(f"✅ 图标转换成功: {ico_path}")
            return ico_path
        else:
            print("❌ 图标文件不存在")
            return None
    except Exception as e:
        print(f"❌ 图标转换失败: {e}")
        return None

def create_simple_spec():
    """创建简化的spec文件"""
    
    icon_path = convert_icon()
    icon_line = f"    icon='{icon_path}'," if icon_path else ""
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('img', 'img'),
        ('cloudflare_bypass_helper.py', '.'),
        ('gmgn_fetcher.py', '.'),
        ('TelegramBot.py', '.'),
        ('CloudflareBypasser.py', '.'),
    ],
    hiddenimports=[
        'telegram',
        'telegram.ext',
        'DrissionPage',
        'bs4',
        'requests',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        'threading',
        'logging',
        'json',
        'pickle',
        'urllib.parse',
        'datetime',
        'collections',
        'openpyxl',
        'DataRecorder',
        'DataRecorder.byte_recorder',
        'DataRecorder.base',
        'DataRecorder.setter',
        'schedule',
        'asyncio',
        'concurrent.futures',
        'websockets',
        'aiohttp',
        'lxml',
        'lxml.etree',
        'lxml.html',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'pyarrow',
        'psutil',
        'pytest',
        'IPython',
        'jupyter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SolanaTokenBot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
{icon_line}
)
'''
    
    with open('simple_gui.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 简化spec文件创建成功")

def build_simple():
    """构建简化版本"""
    try:
        print("🚀 开始构建简化版本...")
        
        cmd = [sys.executable, '-m', 'PyInstaller', 'simple_gui.spec', '--clean', '--noconfirm']
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print("📁 可执行文件: dist/SolanaTokenBot.exe")
            return True
        else:
            print("❌ 构建失败！")
            print("错误信息:")
            print(result.stderr[-2000:])  # 只显示最后2000字符
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🎯 简化版GUI打包工具")
    print("="*40)
    
    print("📦 创建简化配置...")
    create_simple_spec()
    
    print("\n🔨 开始构建...")
    success = build_simple()
    
    if success:
        print("\n🎉 打包成功！")
        print("\n📁 生成的文件:")
        print("   - dist/SolanaTokenBot.exe")
        print("   - img/icon.ico")
        
        print("\n✨ 程序特性:")
        print("   🖼️  自定义图标")
        print("   🔇 无头模式")
        print("   📊 GUI界面")
        print("   📝 实时日志")
        print("   ❌ 一键关闭")
        
    else:
        print("\n❌ 打包失败")
        print("可能的解决方案:")
        print("1. 降级NumPy: pip install 'numpy<2'")
        print("2. 更新依赖: pip install --upgrade matplotlib")
        print("3. 使用虚拟环境重新安装依赖")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
