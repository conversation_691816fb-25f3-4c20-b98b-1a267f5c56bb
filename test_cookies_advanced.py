#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级cookies测试 - 解决Cloudflare绕过后的使用问题
"""

import requests
import json
import time
import logging
from datetime import datetime
from cloudflare_bypass_helper import fetch_page_data, load_cookies_from_file, get_domain_from_url

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AdvancedCookiesManager:
    def __init__(self):
        self.session = requests.Session()
        self.last_bypass_time = {}
        
    def setup_session_headers(self):
        """设置与浏览器一致的请求头"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
    
    def load_cookies_to_session(self, domain):
        """将保存的cookies加载到session中"""
        cookies = load_cookies_from_file(domain)
        if cookies:
            # 清除旧cookies
            self.session.cookies.clear()
            
            # 添加新cookies
            for name, value in cookies.items():
                self.session.cookies.set(name, value, domain=domain)
            
            logging.info(f"已加载 {len(cookies)} 个cookies到session: {list(cookies.keys())}")
            return True
        else:
            logging.warning(f"没有找到 {domain} 的cookies")
            return False
    
    def bypass_and_setup_session(self, url):
        """绕过Cloudflare并设置session"""
        domain = get_domain_from_url(url)
        
        logging.info(f"🔄 开始绕过 {domain} 并设置session...")
        
        # 1. 使用现有方法绕过Cloudflare
        result = fetch_page_data(url)
        
        if not result:
            logging.error("Cloudflare绕过失败")
            return False
        
        # 2. 设置session headers
        self.setup_session_headers()
        
        # 3. 加载cookies到session
        if not self.load_cookies_to_session(domain):
            logging.error("无法加载cookies到session")
            return False
        
        # 4. 记录绕过时间
        self.last_bypass_time[domain] = datetime.now()
        
        logging.info("✅ Session设置完成")
        return True
    
    def test_session_request(self, url):
        """使用session测试请求"""
        logging.info(f"🧪 测试session请求: {url}")
        
        try:
            # 添加Referer头（重要！）
            headers = {
                'Referer': f"https://{get_domain_from_url(url)}/"
            }
            
            response = self.session.get(url, headers=headers, timeout=15)

            logging.info(f"响应状态码: {response.status_code}")
            logging.info(f"响应Content-Type: {response.headers.get('content-type', 'unknown')}")
            logging.info(f"响应Content-Encoding: {response.headers.get('content-encoding', 'none')}")
            logging.info(f"响应长度: {len(response.content)} bytes")

            if response.status_code == 200:
                # 检查是否被Cloudflare拦截
                if "Just a moment" in response.text or "Checking your browser" in response.text:
                    logging.warning("仍然被Cloudflare拦截")
                    return None
                
                # 尝试解析数据
                try:
                    if 'application/json' in response.headers.get('content-type', ''):
                        data = response.json()
                        logging.info("✅ 成功获取JSON数据")
                        return data
                    else:
                        # HTML格式，尝试从<pre>标签提取
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(response.text, 'html.parser')
                        pre_content = soup.find('pre')
                        if pre_content:
                            data = json.loads(pre_content.text)
                            logging.info("✅ 从HTML <pre>标签成功提取JSON数据")
                            return data
                        else:
                            logging.warning("响应不包含JSON数据")
                            # 保存响应内容用于调试
                            with open('debug_response.html', 'w', encoding='utf-8') as f:
                                f.write(response.text[:2000])  # 只保存前2000字符
                            logging.info("响应内容已保存到 debug_response.html")
                            return None
                except json.JSONDecodeError as e:
                    logging.error(f"JSON解析失败: {e}")
                    return None
            else:
                logging.error(f"HTTP错误: {response.status_code}")
                # 保存错误响应（处理编码问题）
                try:
                    # 检查响应编码
                    if response.headers.get('content-encoding') == 'br':
                        # Brotli压缩
                        import brotli
                        decompressed = brotli.decompress(response.content)
                        error_text = decompressed.decode('utf-8')[:1000]
                    elif response.headers.get('content-encoding') == 'gzip':
                        # Gzip压缩
                        import gzip
                        decompressed = gzip.decompress(response.content)
                        error_text = decompressed.decode('utf-8')[:1000]
                    else:
                        # 无压缩
                        error_text = response.text[:1000]

                    with open('debug_error_response.txt', 'w', encoding='utf-8') as f:
                        f.write(error_text)
                    logging.info("错误响应文本已保存到 debug_error_response.txt")

                    # 检查是否是Cloudflare错误页面
                    if "cloudflare" in error_text.lower() or "ray id" in error_text.lower():
                        logging.warning("这是Cloudflare错误页面")

                except Exception as e:
                    logging.error(f"解压缩响应失败: {e}")
                    # 保存原始字节
                    with open('debug_error_response.bin', 'wb') as f:
                        f.write(response.content[:1000])
                    logging.info("错误响应字节已保存到 debug_error_response.bin")

                # 检查是否是Cloudflare错误页面
                if "cloudflare" in response.text.lower() or "ray id" in response.text.lower():
                    logging.warning("这是Cloudflare错误页面")

                return None
                
        except Exception as e:
            logging.error(f"请求异常: {e}")
            return None
    
    def check_extracted_data(self):
        """检查绕过时提取的数据"""
        try:
            with open('extracted_data.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logging.info("📋 检查extracted_data.json内容:")
            if isinstance(data, dict):
                if 'data' in data:
                    if isinstance(data['data'], list):
                        logging.info(f"  包含 {len(data['data'])} 条记录")
                    elif isinstance(data['data'], dict) and 'tokens' in data['data']:
                        logging.info(f"  包含 {len(data['data']['tokens'])} 个tokens")
                    else:
                        logging.info(f"  data字段类型: {type(data['data'])}")
                else:
                    logging.info(f"  根级别字段: {list(data.keys())}")
            else:
                logging.info(f"  数据类型: {type(data)}")
                
            return data
        except FileNotFoundError:
            logging.warning("extracted_data.json 文件不存在")
            return None
        except Exception as e:
            logging.error(f"读取extracted_data.json失败: {e}")
            return None

def test_advanced_cookies():
    """测试高级cookies管理"""
    print("🧪 开始高级cookies测试")
    print("=" * 60)
    
    manager = AdvancedCookiesManager()
    
    # 测试URL
    test_url = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10"
    
    print(f"\n📋 测试URL: {test_url}")
    print("-" * 40)
    
    # 1. 绕过并设置session
    if not manager.bypass_and_setup_session(test_url):
        print("❌ Session设置失败")
        return
    
    # 2. 检查绕过时提取的数据
    print(f"\n📊 检查绕过时提取的数据:")
    print("-" * 40)
    extracted_data = manager.check_extracted_data()
    
    # 3. 使用session测试请求
    print(f"\n🧪 使用session测试请求:")
    print("-" * 40)
    
    result = manager.test_session_request(test_url)
    
    if result:
        data_count = 0
        if isinstance(result, dict) and 'data' in result:
            data_count = len(result['data']) if isinstance(result['data'], list) else 1
        
        print(f"✅ Session请求成功，获取 {data_count} 条数据")
        
        # 比较两次获取的数据
        if extracted_data and result:
            print(f"\n🔍 数据对比:")
            print(f"  绕过时数据: {type(extracted_data)} - {len(extracted_data.get('data', [])) if isinstance(extracted_data, dict) else 'N/A'}")
            print(f"  Session数据: {type(result)} - {len(result.get('data', [])) if isinstance(result, dict) else 'N/A'}")
    else:
        print(f"❌ Session请求失败")
        
        # 如果session失败但绕过时有数据，说明问题在session使用上
        if extracted_data:
            print(f"💡 绕过时成功获取了数据，但session使用失败")
            print(f"   这说明问题在于cookies/headers的使用方式")

if __name__ == "__main__":
    test_advanced_cookies()
