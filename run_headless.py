#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无头模式运行完整的代币分析流程
"""

import logging
import json
import time
import sys
from datetime import datetime
from collections import OrderedDict
from cloudflare_bypass_helper import (
    fetch_page_data, 
    fetch_with_cookies, 
    close_global_driver, 
    set_headless_mode
)
from gmgn_fetcher import (
    filter_tokens, 
    format_tokens, 
    filter_bluechip_tokens, 
    format_bluechip_tokens, 
    format_bluechip_tokens_with_price_info,
    ca_format_tg_message
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# API URLs
GMGN_API_URL = "https://gmgn.ai/defi/quotation/v1/tokens/sol"
BLUECHIP_API_URL = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=100"

class HeadlessTokenAnalyzer:
    def __init__(self):
        # 启用无头模式
        set_headless_mode(True)
        print("🔇 无头模式已启用 - 浏览器窗口不可见")
        
        self.sent_tokens_cache = OrderedDict()
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'regular_tokens': [],
            'bluechip_tokens': [],
            'new_tokens': [],
            'price_update_tokens': [],
            'statistics': {}
        }

    def run_analysis(self):
        """运行完整分析"""
        print("🚀 开始无头模式代币分析...")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        try:
            # 1. 常规代币分析
            print("🔍 常规代币分析...")
            regular_success = self._analyze_regular_tokens()
            
            # 2. 蓝筹代币分析
            print("\n💎 蓝筹代币分析...")
            bluechip_success = self._analyze_bluechip_tokens()
            
            # 3. 生成报告
            print("\n📄 生成分析报告...")
            report_success = self._generate_reports()
            
            # 统计结果
            total_success = sum([regular_success, bluechip_success, report_success])
            
            print("\n" + "="*60)
            print("📊 分析结果:")
            print("="*60)
            print(f"常规代币分析: {'✅ 成功' if regular_success else '❌ 失败'}")
            print(f"蓝筹代币分析: {'✅ 成功' if bluechip_success else '❌ 失败'}")
            print(f"报告生成: {'✅ 成功' if report_success else '❌ 失败'}")
            print(f"总体成功率: {total_success}/3")
            
            if total_success >= 2:
                print("\n🎉 分析完成！")
                self._print_summary()
                return True
            else:
                print("\n⚠️  分析部分失败")
                return False
                
        except Exception as e:
            print(f"\n❌ 分析过程发生错误: {e}")
            logging.error("Analysis failed", exc_info=True)
            return False
        finally:
            close_global_driver()
            print("\n🧹 浏览器资源已清理")

    def _analyze_regular_tokens(self):
        """分析常规代币"""
        try:
            # 获取数据
            tokens_data = fetch_page_data(GMGN_API_URL)
            
            if not tokens_data or "data" not in tokens_data or "tokens" not in tokens_data["data"]:
                print("   ❌ 获取代币数据失败")
                return False
            
            tokens = tokens_data["data"]["tokens"]
            print(f"   ✅ 获取到 {len(tokens)} 个代币")
            
            # 筛选
            filtered_tokens = filter_tokens(
                tokens,
                min_liquidity=120000,
                min_volume=200000,
                max_holders_rate=0.2
            )
            
            print(f"   ✅ 筛选出 {len(filtered_tokens)} 个符合条件的代币")
            
            # 保存结果
            self.results['regular_tokens'] = filtered_tokens
            self.results['statistics']['regular_tokens_count'] = len(filtered_tokens)
            
            if filtered_tokens:
                # 显示预览
                for i, token in enumerate(filtered_tokens[:3]):
                    symbol = token.get('symbol', 'Unknown')
                    price = float(token.get('price', 0))
                    volume = float(token.get('volume_24h', 0))
                    print(f"   {i+1}. {symbol} - ${price:.6f} - ${volume:,.0f}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 常规代币分析失败: {e}")
            return False

    def _analyze_bluechip_tokens(self):
        """分析蓝筹代币"""
        try:
            # 获取数据
            response = fetch_with_cookies(BLUECHIP_API_URL)
            
            if not response or "data" not in response:
                print("   ❌ 获取蓝筹代币数据失败")
                return False
            
            tokens = response["data"]
            print(f"   ✅ 获取到 {len(tokens)} 个蓝筹代币")
            
            # 筛选
            filtered_tokens = filter_bluechip_tokens(tokens, min_volume=20_000)
            print(f"   ✅ 筛选出 {len(filtered_tokens)} 个符合条件的蓝筹代币")
            
            # 分析新代币和价格变化
            new_tokens = []
            price_update_tokens = []
            
            for token in filtered_tokens:
                address = token['address']
                current_price = float(token.get('price', 0))
                
                if address not in self.sent_tokens_cache:
                    # 新代币
                    new_tokens.append(token)
                    self.sent_tokens_cache[address] = {
                        'price': current_price,
                        'timestamp': datetime.now().isoformat(),
                        'last_notified_multiplier': 1.0
                    }
                else:
                    # 检查价格变化
                    cached_info = self.sent_tokens_cache[address]
                    if isinstance(cached_info, dict) and 'price' in cached_info:
                        original_price = cached_info['price']
                        if original_price > 0:
                            current_multiplier = current_price / original_price
                            last_notified_multiplier = cached_info.get('last_notified_multiplier', 1.0)
                            next_threshold = last_notified_multiplier * 2
                            
                            if current_multiplier >= next_threshold:
                                price_update_tokens.append(token)
                                self.sent_tokens_cache[address]['last_notified_multiplier'] = current_multiplier
            
            print(f"   ✅ 发现 {len(new_tokens)} 个新代币")
            print(f"   ✅ 发现 {len(price_update_tokens)} 个价格翻倍代币")
            
            # 保存结果
            self.results['bluechip_tokens'] = filtered_tokens
            self.results['new_tokens'] = new_tokens
            self.results['price_update_tokens'] = price_update_tokens
            self.results['statistics']['bluechip_tokens_count'] = len(filtered_tokens)
            self.results['statistics']['new_tokens_count'] = len(new_tokens)
            self.results['statistics']['price_update_tokens_count'] = len(price_update_tokens)
            
            # 显示新代币预览
            if new_tokens:
                print("   🆕 新代币预览:")
                for i, token in enumerate(new_tokens[:3]):
                    symbol = token.get('symbol', 'Unknown')
                    price = float(token.get('price', 0))
                    volume = float(token.get('volume', 0))
                    print(f"      {i+1}. {symbol} - ${price:.6f} - ${volume:,.0f}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 蓝筹代币分析失败: {e}")
            return False

    def _generate_reports(self):
        """生成分析报告"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 1. 生成常规代币报告
            if self.results['regular_tokens']:
                formatted_messages = format_tokens(self.results['regular_tokens'])
                with open(f'regular_tokens_{timestamp}.txt', 'w', encoding='utf-8') as f:
                    f.write(f"常规代币分析 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("="*60 + "\n\n")
                    f.write(formatted_messages)
                print(f"   ✅ 常规代币报告: regular_tokens_{timestamp}.txt")
            
            # 2. 生成蓝筹代币报告
            if self.results['new_tokens'] or self.results['price_update_tokens']:
                tokens_to_process = []
                
                # 添加新代币
                for token in self.results['new_tokens']:
                    tokens_to_process.append({
                        'token': token,
                        'is_price_update': False,
                        'price_multiplier': None,
                        'original_price': float(token.get('price', 0))
                    })
                
                # 添加价格翻倍代币
                for token in self.results['price_update_tokens']:
                    address = token['address']
                    current_price = float(token.get('price', 0))
                    if address in self.sent_tokens_cache:
                        original_price = self.sent_tokens_cache[address]['price']
                        multiplier = current_price / original_price if original_price > 0 else 0
                        tokens_to_process.append({
                            'token': token,
                            'is_price_update': True,
                            'price_multiplier': multiplier,
                            'original_price': original_price
                        })
                
                if tokens_to_process:
                    formatted_messages = format_bluechip_tokens_with_price_info(tokens_to_process)
                    with open(f'bluechip_tokens_{timestamp}.txt', 'w', encoding='utf-8') as f:
                        f.write(f"蓝筹代币分析 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("="*60 + "\n\n")
                        for i, message in enumerate(formatted_messages, 1):
                            f.write(f"代币 {i}:\n{message}\n{'-'*40}\n\n")
                    print(f"   ✅ 蓝筹代币报告: bluechip_tokens_{timestamp}.txt")
            
            # 3. 生成JSON数据
            with open(f'analysis_data_{timestamp}.json', 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
            print(f"   ✅ 完整数据: analysis_data_{timestamp}.json")
            
            # 4. 生成总结报告
            with open(f'summary_{timestamp}.txt', 'w', encoding='utf-8') as f:
                f.write(f"代币分析总结 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("="*60 + "\n\n")
                f.write("📊 统计信息:\n")
                stats = self.results['statistics']
                f.write(f"   常规代币: {stats.get('regular_tokens_count', 0)}\n")
                f.write(f"   蓝筹代币: {stats.get('bluechip_tokens_count', 0)}\n")
                f.write(f"   新代币: {stats.get('new_tokens_count', 0)}\n")
                f.write(f"   价格翻倍: {stats.get('price_update_tokens_count', 0)}\n")
            print(f"   ✅ 总结报告: summary_{timestamp}.txt")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 报告生成失败: {e}")
            return False

    def _print_summary(self):
        """打印分析总结"""
        stats = self.results['statistics']
        
        print("\n📊 分析总结:")
        print(f"   常规代币数量: {stats.get('regular_tokens_count', 0)}")
        print(f"   蓝筹代币数量: {stats.get('bluechip_tokens_count', 0)}")
        print(f"   新发现代币: {stats.get('new_tokens_count', 0)}")
        print(f"   价格翻倍代币: {stats.get('price_update_tokens_count', 0)}")
        
        print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("📁 生成的文件已保存到当前目录")

def main():
    """主函数"""
    print("🔇 无头模式代币分析器")
    print("="*40)
    
    try:
        analyzer = HeadlessTokenAnalyzer()
        success = analyzer.run_analysis()
        return success
        
    except KeyboardInterrupt:
        print("\n\n⚠️  分析被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        logging.error("Main function failed", exc_info=True)
        return False
    finally:
        try:
            close_global_driver()
        except:
            pass

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 程序发生未预期的错误: {e}")
        logging.error("Unexpected error", exc_info=True)
        sys.exit(1)
