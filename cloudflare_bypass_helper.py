import os
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup
import logging
import json
import pickle
import requests
from CloudflareBypasser import CloudflareBypasser

# 全局变量存储浏览器实例和cookies
_global_driver = None
_cookies_cache = {}
_cookies_file = 'cloudflare_cookies.pkl'
_headless_mode = False  # 默认显示浏览器窗口

# Cookies状态管理
_cookies_status = {
    'gmgn.ai': {
        'valid': False,
        'last_update': None,
        'retry_count': 0
    }
}
_MAX_RETRY_COUNT = 3

def get_chromium_options(browser_path: str, arguments: list):
    """
    配置并返回 Chromium 浏览器选项。

    :param browser_path: Chromium 浏览器可执行文件的路径。
    :param arguments: 浏览器的启动参数列表。
    :return: 配置好的 ChromiumOptions 实例。
    """
    options = ChromiumOptions()
    options.set_paths(browser_path=browser_path)
    for argument in arguments:
        options.set_argument(argument)
    return options

def save_cookies_to_file(cookies, domain):
    """
    保存cookies到文件
    """
    try:
        if os.path.exists(_cookies_file):
            with open(_cookies_file, 'rb') as f:
                all_cookies = pickle.load(f)
        else:
            all_cookies = {}

        all_cookies[domain] = cookies

        with open(_cookies_file, 'wb') as f:
            pickle.dump(all_cookies, f)
        logging.info(f"Cookies saved for domain: {domain}")
    except Exception as e:
        logging.error(f"Failed to save cookies: {e}")

def load_cookies_from_file(domain):
    """
    从文件加载cookies
    """
    try:
        if os.path.exists(_cookies_file):
            with open(_cookies_file, 'rb') as f:
                all_cookies = pickle.load(f)
            return all_cookies.get(domain, {})
    except Exception as e:
        logging.error(f"Failed to load cookies: {e}")
    return {}

def get_domain_from_url(url):
    """
    从URL提取域名
    """
    from urllib.parse import urlparse
    return urlparse(url).netloc

def set_headless_mode(headless=True):
    """
    设置无头模式
    :param headless: True为无头模式（不显示浏览器），False为显示浏览器
    """
    global _headless_mode
    _headless_mode = headless
    logging.info(f"无头模式设置为: {headless}")

def get_headless_mode():
    """
    获取当前无头模式设置
    """
    global _headless_mode
    return _headless_mode

def get_or_create_driver():
    """
    获取或创建全局浏览器实例
    """
    global _global_driver, _headless_mode

    if _global_driver is None:
        # 判断是否为无头模式（优先使用全局设置，其次使用环境变量）
        is_headless = _headless_mode or os.getenv('HEADLESS', 'false').lower() == 'true'

        # Windows系统下设置Chrome路径
        if os.name == 'nt':  # Windows
            browser_path = os.getenv('CHROME_PATH', r"C:\Program Files\Google\Chrome\Application\chrome.exe")
        else:  # Linux/Mac
            browser_path = os.getenv('CHROME_PATH', "/usr/bin/google-chrome")

        # 浏览器启动参数
        arguments = [
            "-no-first-run",
            "-force-color-profile=srgb",
            "-metrics-recording-only",
            "-password-store=basic",
            "-use-mock-keychain",
            "-no-default-browser-check",
            "-disable-background-mode",
            "-enable-features=NetworkService,NetworkServiceInProcess",
            "-disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage",
            "-deny-permission-prompts",
            "-disable-gpu",
            "-accept-lang=en-US",
        ]

        # 如果是无头模式，添加无头参数
        if is_headless:
            arguments.extend([
                "--headless",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-blink-features=AutomationControlled",  # 避免被检测为自动化
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            ])
            logging.info("启用无头模式 (浏览器窗口不可见)")
        else:
            logging.info("启用有头模式 (浏览器窗口可见)")

        # 配置 Chromium 浏览器
        options = get_chromium_options(browser_path, arguments)

        # 初始化浏览器
        _global_driver = ChromiumPage(addr_or_opts=options)
        logging.info("Created new browser instance")

    return _global_driver

def close_global_driver():
    """
    关闭全局浏览器实例
    """
    global _global_driver
    if _global_driver:
        _global_driver.quit()
        _global_driver = None
        logging.info("Global browser instance closed")

def fetch_page_data(url, use_cached_cookies=True):
    """
    绕过 Cloudflare 并提取页面数据，支持cookies缓存。

    :param url: 要访问的URL
    :param use_cached_cookies: 是否使用缓存的cookies
    :return: 解析后的JSON数据
    """
    domain = get_domain_from_url(url)

    # 首先尝试使用缓存的cookies直接请求
    if use_cached_cookies:
        cached_cookies = load_cookies_from_file(domain)
        if cached_cookies:
            try:
                response = requests.get(url, cookies=cached_cookies, timeout=10)
                if response.status_code == 200 and "Just a moment" not in response.text:
                    # 尝试解析JSON
                    soup = BeautifulSoup(response.text, 'html.parser')
                    pre_content = soup.find('pre')
                    if pre_content:
                        json_data = pre_content.text
                        parsed_data = json.loads(json_data)
                        logging.info("Successfully used cached cookies")
                        return parsed_data
                else:
                    logging.info("Cached cookies expired or invalid, need to bypass Cloudflare")
            except Exception as e:
                logging.warning(f"Failed to use cached cookies: {e}")

    # 如果缓存的cookies无效，使用浏览器绕过Cloudflare
    driver = get_or_create_driver()

    try:
        logging.info('Navigating to the demo page.')
        driver.get(url)

        # 开始绕过 Cloudflare
        logging.info('Starting Cloudflare bypass.')
        cf_bypasser = CloudflareBypasser(driver)
        cf_bypasser.bypass()

        # 成功绕过后获取页面内容
        logging.info("Enjoy the content!")
        logging.info("Title of the page: %s", driver.title)

        # 获取cookies并保存
        cookies = {}
        for cookie in driver.cookies():
            cookies[cookie['name']] = cookie['value']

        save_cookies_to_file(cookies, domain)
        logging.info(f"Saved {len(cookies)} cookies for domain: {domain}")

        # 从 driver.html 提取页面 HTML
        page_source = driver.html

        # 使用 BeautifulSoup 解析 HTML
        soup = BeautifulSoup(page_source, 'html.parser')

        # 查找 <pre> 标签，提取 JSON 数据
        pre_content = soup.find('pre')
        if pre_content:
            json_data = pre_content.text  # 提取 JSON 文本
            parsed_data = json.loads(json_data)  # 解析 JSON 数据
            logging.info("JSON data extracted successfully.")

            # 保存 JSON 数据到文件
            output_file = 'extracted_data.json'
            with open(output_file, 'w', encoding='utf-8') as file:
                json.dump(parsed_data, file, indent=4, ensure_ascii=False)
                logging.info(f"JSON data saved to {output_file}")
            return parsed_data
        else:
            logging.error("Failed to extract JSON data.")
            return None
    except Exception as e:
        logging.error(f"Error during Cloudflare bypass: {e}")
        return None

def fetch_with_cookies(url):
    """
    使用保存的cookies直接请求API

    :param url: API URL
    :return: 解析后的JSON数据
    """
    domain = get_domain_from_url(url)
    cached_cookies = load_cookies_from_file(domain)

    if not cached_cookies:
        logging.warning("No cached cookies found, need to bypass Cloudflare first")
        return fetch_page_data(url)

    # 构建更完整的请求头，模拟浏览器行为
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }

    try:
        response = requests.get(url, cookies=cached_cookies, headers=headers, timeout=15)

        if response.status_code == 200:
            if "Just a moment" in response.text or "Checking your browser" in response.text:
                logging.info("Cookies expired or Cloudflare challenge detected, need to re-bypass")
                return fetch_page_data(url, use_cached_cookies=False)

            # 尝试解析JSON
            soup = BeautifulSoup(response.text, 'html.parser')
            pre_content = soup.find('pre')
            if pre_content:
                json_data = pre_content.text
                try:
                    parsed_data = json.loads(json_data)
                    logging.info("Successfully fetched data with cached cookies (HTML format)")
                    return parsed_data
                except json.JSONDecodeError:
                    logging.error("Failed to parse JSON from HTML pre tag")
                    return None
            else:
                # 如果不是HTML格式，直接尝试解析JSON
                try:
                    parsed_data = response.json()
                    logging.info("Successfully fetched JSON data with cached cookies")
                    return parsed_data
                except json.JSONDecodeError:
                    # 如果也不是JSON，可能是其他格式的成功响应
                    logging.warning("Response is not JSON format, returning raw text")
                    return {"raw_content": response.text[:1000]}  # 返回前1000字符
        elif response.status_code == 403:
            logging.info("403 Forbidden - Cookies may be expired, trying to re-bypass Cloudflare")
            return fetch_page_data(url, use_cached_cookies=False)
        elif response.status_code == 429:
            logging.warning("429 Too Many Requests - Rate limited")
            return None
        else:
            logging.error(f"HTTP error: {response.status_code}")
            return None
    except requests.exceptions.Timeout:
        logging.error("Request timeout")
        return None
    except requests.exceptions.ConnectionError:
        logging.error("Connection error")
        return None
    except Exception as e:
        logging.error(f"Error fetching with cookies: {e}")
        return None
