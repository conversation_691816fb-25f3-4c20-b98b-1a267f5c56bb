#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带GUI界面的TG机器人主程序
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import logging
import sys
import os
from datetime import datetime
from TelegramBot import TelegramBot
from cloudflare_bypass_helper import close_global_driver

class TelegramBotGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Solana代币分析机器人")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # 机器人实例
        self.bot = None
        self.bot_thread = None
        self.is_running = False

        # 创建GUI界面
        self.create_widgets()

        # 设置图标（在界面创建后）
        self.set_icon()

        # 设置日志
        self.setup_logging()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def set_icon(self):
        """设置应用程序图标"""
        try:
            # 尝试多个可能的图标路径
            possible_paths = [
                os.path.join("img", "狗头.jpg"),
                os.path.join("img", "icon.ico"),
                "img/狗头.jpg",
                "img/icon.ico"
            ]

            icon_found = False

            for icon_path in possible_paths:
                if os.path.exists(icon_path):
                    try:
                        if icon_path.endswith('.ico'):
                            # 直接使用ICO文件
                            self.root.iconbitmap(icon_path)
                            self.log_message("✅ ICO图标设置成功")
                            icon_found = True
                            break
                        else:
                            # 对于JPG图片，使用PIL转换
                            from PIL import Image, ImageTk
                            # 加载并调整图片大小
                            img = Image.open(icon_path)
                            img = img.resize((32, 32), Image.Resampling.LANCZOS)

                            # 转换为PhotoImage
                            photo = ImageTk.PhotoImage(img)
                            self.root.iconphoto(False, photo)

                            # 保持引用避免被垃圾回收
                            self.icon_photo = photo

                            self.log_message("✅ JPG图标设置成功")
                            icon_found = True
                            break
                    except ImportError:
                        self.log_message("⚠️  PIL库未安装，尝试其他图标格式")
                        continue
                    except Exception as e:
                        self.log_message(f"⚠️  图标 {icon_path} 设置失败: {e}")
                        continue

            if not icon_found:
                self.log_message("⚠️  未找到图标文件，使用默认图标")

        except Exception as e:
            self.log_message(f"⚠️  图标设置出错: {e}")

    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🚀 Solana代币分析机器人", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 状态框架
        status_frame = ttk.LabelFrame(main_frame, text="📊 运行状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态指示器
        ttk.Label(status_frame, text="机器人状态:").grid(row=0, column=0, sticky=tk.W)
        self.status_var = tk.StringVar(value="🔴 未启动")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                     font=("Arial", 10, "bold"))
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 无头模式状态
        ttk.Label(status_frame, text="无头模式:").grid(row=1, column=0, sticky=tk.W)
        self.headless_var = tk.StringVar(value="🔇 已启用")
        ttk.Label(status_frame, textvariable=self.headless_var, 
                 font=("Arial", 10)).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 最后活动时间
        ttk.Label(status_frame, text="最后活动:").grid(row=2, column=0, sticky=tk.W)
        self.last_activity_var = tk.StringVar(value="无")
        ttk.Label(status_frame, textvariable=self.last_activity_var, 
                 font=("Arial", 10)).grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="📝 运行日志", padding="10")
        log_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        # 启动按钮
        self.start_button = ttk.Button(button_frame, text="🚀 启动机器人", 
                                      command=self.start_bot, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="⏹️ 停止机器人", 
                                     command=self.stop_bot, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空日志按钮
        clear_button = ttk.Button(button_frame, text="🗑️ 清空日志", 
                                 command=self.clear_log)
        clear_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 关闭按钮
        close_button = ttk.Button(button_frame, text="❌ 关闭程序", 
                                 command=self.on_closing)
        close_button.pack(side=tk.RIGHT)
        
        # 初始日志消息
        self.log_message("🎯 程序已启动，等待用户操作...")
        self.log_message("💡 点击'启动机器人'开始运行")
        self.log_message("🔇 无头模式已启用 - 浏览器窗口不可见")

    def setup_logging(self):
        """设置日志系统"""
        # 创建自定义日志处理器
        class GUILogHandler(logging.Handler):
            def __init__(self, gui_instance):
                super().__init__()
                self.gui = gui_instance

            def emit(self, record):
                msg = self.format(record)
                # 在主线程中更新GUI
                self.gui.root.after(0, lambda: self.gui.log_message(msg))

        # 配置日志，过滤掉异步警告
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                GUILogHandler(self),
                logging.FileHandler('bot_gui.log', encoding='utf-8')
            ]
        )

        # 设置特定日志级别以减少警告
        logging.getLogger('asyncio').setLevel(logging.ERROR)
        logging.getLogger('telegram').setLevel(logging.WARNING)
        logging.getLogger('httpx').setLevel(logging.WARNING)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新GUI
        if threading.current_thread() == threading.main_thread():
            self.log_text.insert(tk.END, formatted_message)
            self.log_text.see(tk.END)
        else:
            self.root.after(0, lambda: self._update_log(formatted_message))
    
    def _update_log(self, message):
        """在主线程中更新日志"""
        self.log_text.insert(tk.END, message)
        self.log_text.see(tk.END)

    def update_status(self, status, activity=None):
        """更新状态显示"""
        def _update():
            self.status_var.set(status)
            if activity:
                self.last_activity_var.set(activity)
        
        if threading.current_thread() == threading.main_thread():
            _update()
        else:
            self.root.after(0, _update)

    def start_bot(self):
        """启动机器人"""
        if self.is_running:
            self.log_message("⚠️  机器人已在运行中")
            return
        
        try:
            self.log_message("🚀 正在启动Telegram机器人...")
            self.update_status("🟡 启动中...")
            
            # 禁用启动按钮，启用停止按钮
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            
            # 创建机器人实例
            BOT_TOKEN = "7616525518:AAGWbdc9y5pu9o9UVFkOEnhXN7Yk8TJVY-E"
            self.bot = TelegramBot(BOT_TOKEN)

            # 立即设置GUI回调函数
            def gui_log_callback(message):
                try:
                    # 使用after方法确保在主线程中执行GUI更新
                    def update_log():
                        self.log_message(message)

                    def update_status_based_on_message():
                        if "接收到" in message:
                            if "/start" in message:
                                self.update_status("🟢 运行中", "处理/start命令")
                            elif "/test" in message:
                                self.update_status("🟢 运行中", "处理/test命令")
                            elif "/pushing" in message:
                                self.update_status("🟢 运行中", "处理/pushing命令")
                            elif "/JK" in message:
                                self.update_status("🟢 运行中", "启动定时任务")
                            elif "/ca" in message:
                                self.update_status("🟢 运行中", "查询代币信息")

                    self.root.after(0, update_log)
                    self.root.after(0, update_status_based_on_message)
                except Exception as e:
                    print(f"GUI回调错误: {e}")

            # 设置回调函数
            self.bot.gui_callback = gui_log_callback
            self.log_message("✅ GUI回调已设置")

            # 在新线程中运行机器人
            self.bot_thread = threading.Thread(target=self.run_bot, daemon=True)
            self.bot_thread.start()
            
            self.is_running = True
            self.log_message("✅ 机器人启动成功！")
            self.update_status("🟢 运行中", "机器人已启动")
            
        except Exception as e:
            self.log_message(f"❌ 机器人启动失败: {e}")
            self.update_status("🔴 启动失败")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

    def run_bot(self):
        """在后台线程中运行机器人"""
        try:
            import asyncio
            import logging
            import warnings
            import sys

            # 完全忽略异步相关的警告和错误
            warnings.filterwarnings("ignore", category=RuntimeWarning)
            warnings.filterwarnings("ignore", category=DeprecationWarning)
            warnings.filterwarnings("ignore", message=".*Task.*attached to a different loop.*")

            # 重定向stderr以捕获异步错误
            class SilentStderr:
                def write(self, s):
                    if "Task exception was never retrieved" not in s and "attached to a different loop" not in s:
                        pass  # 静默处理异步错误
                def flush(self):
                    pass

            original_stderr = sys.stderr
            sys.stderr = SilentStderr()

            # 为新线程创建事件循环
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # 设置日志级别以减少警告
            logging.getLogger('asyncio').setLevel(logging.CRITICAL)
            logging.getLogger('telegram').setLevel(logging.WARNING)
            logging.getLogger('httpx').setLevel(logging.WARNING)

            # 回调函数已在start_bot中设置，这里不需要重复设置

            # 运行机器人
            self.log_message("🔄 正在连接Telegram服务器...")

            # 添加连接超时和错误处理
            try:
                # 先测试连接
                import asyncio
                async def test_connection():
                    try:
                        bot_info = await self.bot.application.bot.get_me()
                        self.log_message(f"✅ 连接成功！机器人: @{bot_info.username}")
                        return True
                    except Exception as e:
                        self.log_message(f"❌ 连接失败: {e}")
                        return False

                # 测试连接
                connected = loop.run_until_complete(test_connection())

                if connected:
                    self.log_message("🚀 开始运行机器人...")
                    self.bot.application.run_polling()
                else:
                    self.log_message("❌ 无法连接到Telegram服务器")
                    self.update_status("🔴 连接失败")

            except Exception as conn_error:
                self.log_message(f"❌ 连接过程出错: {conn_error}")
                self.update_status("🔴 连接错误")

        except Exception as e:
            self.log_message(f"❌ 机器人运行错误: {e}")
            self.update_status("🔴 运行错误")
        finally:
            # 恢复stderr
            try:
                sys.stderr = original_stderr
            except:
                pass

    def stop_bot(self):
        """停止机器人"""
        if not self.is_running:
            self.log_message("⚠️  机器人未在运行")
            return
        
        try:
            self.log_message("⏹️ 正在停止机器人...")
            self.update_status("🟡 停止中...")
            
            if self.bot and self.bot.application:
                # 停止机器人
                self.bot.application.stop()
                self.bot.scheduler_running = False
            
            self.is_running = False
            
            # 更新按钮状态
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            
            self.log_message("✅ 机器人已停止")
            self.update_status("🔴 已停止", "机器人已停止")
            
        except Exception as e:
            self.log_message(f"❌ 停止机器人时出错: {e}")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("🗑️ 日志已清空")

    def on_closing(self):
        """关闭程序"""
        self.log_message("🔄 正在关闭程序...")
        
        # 停止机器人
        if self.is_running:
            self.stop_bot()
        
        # 清理浏览器资源
        try:
            close_global_driver()
            self.log_message("🧹 浏览器资源已清理")
        except:
            pass
        
        self.log_message("👋 程序已关闭")
        
        # 关闭窗口
        self.root.quit()
        self.root.destroy()

    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()

def main():
    """主函数"""
    try:
        # 检查依赖
        try:
            import PIL
        except ImportError:
            # 在GUI模式下不使用print，因为没有控制台
            pass

        # 创建并运行GUI
        app = TelegramBotGUI()
        app.run()

    except Exception as e:
        # 在GUI模式下显示错误对话框而不是print
        try:
            import tkinter.messagebox as msgbox
            msgbox.showerror("启动错误", f"程序启动失败: {e}")
        except:
            # 如果连tkinter都无法使用，则静默退出
            pass

if __name__ == "__main__":
    main()
