#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际数据获取功能
"""

import logging
import json
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_actual_data_retrieval():
    """测试实际数据获取"""
    print("=== 测试实际数据获取 ===\n")
    
    try:
        from cloudflare_bypass_helper import fetch_page_data, close_global_driver
        
        # 测试获取代币数据
        print("🎯 测试获取GMGN代币数据...")
        
        # 使用一个简单的API端点
        test_url = "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens/sol?orderby=volume_24h&direction=desc&limit=5"
        
        print(f"请求URL: {test_url}")
        print("正在获取数据...")
        
        data = fetch_page_data(test_url)
        
        if data:
            print("✅ 数据获取成功！")
            print(f"数据类型: {type(data)}")
            
            # 详细分析数据结构
            if isinstance(data, dict):
                print(f"数据键: {list(data.keys())}")
                
                # 检查是否有预期的数据结构
                if 'code' in data:
                    print(f"响应代码: {data['code']}")
                
                if 'msg' in data:
                    print(f"响应消息: {data['msg']}")
                
                if 'data' in data:
                    tokens_data = data['data']
                    print(f"代币数据类型: {type(tokens_data)}")
                    
                    if isinstance(tokens_data, list):
                        print(f"获取到 {len(tokens_data)} 个代币")
                        
                        # 显示前几个代币的信息
                        for i, token in enumerate(tokens_data[:3]):
                            if isinstance(token, dict):
                                symbol = token.get('symbol', 'Unknown')
                                address = token.get('address', 'No address')
                                price = token.get('price', 'No price')
                                volume = token.get('volume_24h', 'No volume')
                                
                                print(f"  代币 {i+1}:")
                                print(f"    符号: {symbol}")
                                print(f"    地址: {address}")
                                print(f"    价格: {price}")
                                print(f"    24h交易量: {volume}")
                                print()
                    else:
                        print(f"代币数据内容: {tokens_data}")
                
                # 保存完整数据到文件以便检查
                with open('actual_data_sample.json', 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                print("✅ 完整数据已保存到 actual_data_sample.json")
                
            else:
                print(f"数据内容预览: {str(data)[:500]}...")
            
            return True
        else:
            print("❌ 数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logging.error("Data retrieval test failed", exc_info=True)
        return False
    finally:
        try:
            close_global_driver()
            print("\n🧹 浏览器资源已清理")
        except:
            pass

def test_specific_token_data():
    """测试特定代币数据获取"""
    print("\n=== 测试特定代币数据获取 ===\n")
    
    try:
        from cloudflare_bypass_helper import fetch_with_cookies, close_global_driver
        
        # 使用一个知名代币地址进行测试
        token_address = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"  # USDC
        
        print(f"测试代币地址: {token_address}")
        
        # 测试获取rug数据
        print("\n1️⃣ 获取Rug数据...")
        rug_url = f"https://gmgn.ai/defi/quotation/v1/tokens/rug_history/sol/{token_address}"
        rug_data = fetch_with_cookies(rug_url)
        
        if rug_data:
            print("✅ Rug数据获取成功")
            print(f"数据类型: {type(rug_data)}")
            if isinstance(rug_data, dict):
                print(f"数据键: {list(rug_data.keys())}")
                if 'data' in rug_data:
                    rug_info = rug_data['data']
                    if isinstance(rug_info, dict) and 'history' in rug_info:
                        history = rug_info['history']
                        print(f"跑路历史记录数量: {len(history)}")
        else:
            print("❌ Rug数据获取失败")
        
        # 测试获取交易数据
        print("\n2️⃣ 获取交易数据...")
        trades_url = f"https://gmgn.ai/defi/quotation/v1/trades/sol/{token_address}?limit=5"
        trades_data = fetch_with_cookies(trades_url)
        
        if trades_data:
            print("✅ 交易数据获取成功")
            print(f"数据类型: {type(trades_data)}")
            if isinstance(trades_data, dict):
                print(f"数据键: {list(trades_data.keys())}")
                if 'data' in trades_data and 'history' in trades_data['data']:
                    trades = trades_data['data']['history']
                    print(f"交易记录数量: {len(trades)}")
        else:
            print("❌ 交易数据获取失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 特定代币数据测试失败: {e}")
        return False
    finally:
        try:
            close_global_driver()
        except:
            pass

def main():
    """主测试函数"""
    print("🚀 测试实际数据获取功能\n")
    
    test_results = []
    
    # 运行测试
    test_results.append(("基础数据获取", test_actual_data_retrieval()))
    test_results.append(("特定代币数据", test_specific_token_data()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 数据获取测试结果:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-"*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed > 0:
        print("\n🎉 数据获取功能正常工作！")
        print("\n📋 获取到的数据可以用于:")
        print("   ✅ 代币筛选和分析")
        print("   ✅ 风险评估 (Rug检查)")
        print("   ✅ 交易历史分析")
        print("   ✅ Telegram机器人推送")
        
        return True
    else:
        print("\n⚠️  数据获取功能需要进一步调试")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        try:
            from cloudflare_bypass_helper import close_global_driver
            close_global_driver()
        except:
            pass
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logging.error("Unexpected error", exc_info=True)
        try:
            from cloudflare_bypass_helper import close_global_driver
            close_global_driver()
        except:
            pass
        sys.exit(1)
