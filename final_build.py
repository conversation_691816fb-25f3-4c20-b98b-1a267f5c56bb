#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终打包脚本 - 避免复杂依赖
"""

import os
import sys
import subprocess
from PIL import Image

def convert_icon():
    """转换图标"""
    try:
        jpg_path = os.path.join("img", "狗头.jpg")
        ico_path = os.path.join("img", "icon.ico")
        
        if os.path.exists(jpg_path):
            img = Image.open(jpg_path)
            icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64)]
            img.save(ico_path, format='ICO', sizes=icon_sizes)
            print(f"✅ 图标转换成功: {ico_path}")
            return ico_path
        else:
            print("❌ 图标文件不存在")
            return None
    except Exception as e:
        print(f"❌ 图标转换失败: {e}")
        return None

def create_final_spec():
    """创建最终的spec文件"""
    
    icon_path = convert_icon()
    icon_line = f"    icon='{icon_path}'," if icon_path else ""
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('img', 'img'),
        ('cloudflare_bypass_helper.py', '.'),
        ('gmgn_fetcher.py', '.'),
        ('TelegramBot.py', '.'),
        ('CloudflareBypasser.py', '.'),
    ],
    hiddenimports=[
        'telegram',
        'telegram.ext',
        'DrissionPage',
        'bs4',
        'requests',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        'openpyxl',
        'DataRecorder',
        'DataRecorder.byte_recorder',
        'DataRecorder.base',
        'DataRecorder.setter',
        'lxml',
        'lxml.etree',
        'lxml.html',
        'schedule',
        'asyncio',
        'threading',
        'logging',
        'json',
        'pickle',
        'urllib.parse',
        'datetime',
        'collections',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'pandas',
        'scipy',
        'pyarrow',
        'psutil',
        'pytest',
        'IPython',
        'jupyter',
        'notebook',
        'qtpy',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        'wx',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SolanaTokenBot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
{icon_line}
)
'''
    
    with open('final_gui.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 最终spec文件创建成功")

def build_final():
    """构建最终版本"""
    try:
        print("🚀 开始构建最终版本...")
        
        cmd = [sys.executable, '-m', 'PyInstaller', 'final_gui.spec', '--clean', '--noconfirm']
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print("📁 可执行文件: dist/SolanaTokenBot.exe")
            
            # 检查文件大小
            exe_path = os.path.join("dist", "SolanaTokenBot.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"📊 文件大小: {file_size:.1f} MB")
            
            return True
        else:
            print("❌ 构建失败！")
            print("错误信息:")
            # 只显示最后的错误信息
            error_lines = result.stderr.split('\n')
            for line in error_lines[-20:]:
                if line.strip():
                    print(f"   {line}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def test_exe():
    """测试EXE文件"""
    exe_path = os.path.join("dist", "SolanaTokenBot.exe")
    
    if not os.path.exists(exe_path):
        print("❌ EXE文件不存在")
        return False
    
    try:
        print("🧪 测试EXE文件启动...")
        
        # 尝试启动EXE（非阻塞）
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待3秒检查是否正常启动
        import time
        time.sleep(3)
        
        poll_result = process.poll()
        
        if poll_result is None:
            print("✅ EXE文件启动成功！")
            print("💡 GUI窗口应该已显示，请手动关闭")
            
            # 等待用户关闭或超时
            try:
                process.wait(timeout=10)
                print("✅ 程序正常关闭")
            except subprocess.TimeoutExpired:
                print("⚠️  超时，强制结束进程")
                process.terminate()
                process.wait()
            
            return True
        else:
            stdout, stderr = process.communicate()
            print("❌ EXE文件启动失败")
            if stderr:
                print(f"错误: {stderr.decode('utf-8', errors='ignore')[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 测试EXE时出错: {e}")
        return False

def main():
    """主函数"""
    print("🎯 最终版GUI打包工具")
    print("="*50)
    
    print("📦 创建最终配置...")
    create_final_spec()
    
    print("\n🔨 开始构建...")
    success = build_final()
    
    if success:
        print("\n🎉 打包成功！")
        
        # 询问是否测试
        try:
            choice = input("\n是否测试EXE文件？[y/N]: ").strip().lower()
            if choice in ['y', 'yes', '是']:
                print("\n🧪 开始测试...")
                test_success = test_exe()
                
                if test_success:
                    print("\n🎉 测试通过！EXE文件完全正常")
                else:
                    print("\n⚠️  测试失败，但EXE文件已生成")
        except KeyboardInterrupt:
            print("\n跳过测试")
        
        print("\n✨ 最终结果:")
        print("   📁 主程序: dist/SolanaTokenBot.exe")
        print("   🖼️ 图标: img/icon.ico")
        print("   📝 配置: final_gui.spec")
        
        print("\n🚀 使用方法:")
        print("   1. 双击 dist/SolanaTokenBot.exe")
        print("   2. 点击'启动机器人'")
        print("   3. 在TG中发送/JK命令")
        print("   4. 享受GUI体验！")
        
        return True
    else:
        print("\n❌ 打包失败")
        print("\n🔧 可能的解决方案:")
        print("   1. 重启命令行")
        print("   2. 清理临时文件: rmdir /s build dist")
        print("   3. 重新安装依赖")
        
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  打包被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程中发生错误: {e}")
        sys.exit(1)
