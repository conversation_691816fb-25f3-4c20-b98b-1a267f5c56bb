#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的API调用场景
"""

import logging
import time
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_gmgn_api_with_cookies():
    """测试GMGN API调用和cookies功能"""
    print("=== 测试GMGN API调用和Cookies功能 ===\n")
    
    try:
        from cloudflare_bypass_helper import (
            fetch_page_data, 
            fetch_with_cookies, 
            close_global_driver,
            load_cookies_from_file,
            get_domain_from_url
        )
        
        # 测试URL - 使用一个相对简单的API端点
        test_url = "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens/sol?orderby=volume_24h&direction=desc&limit=5"
        
        print(f"测试URL: {test_url}")
        print(f"目标域名: {get_domain_from_url(test_url)}")
        
        # 步骤1: 首次访问，绕过Cloudflare
        print("\n1️⃣ 首次访问 - 绕过Cloudflare...")
        start_time = time.time()
        
        try:
            data1 = fetch_page_data(test_url)
            end_time = time.time()
            
            if data1:
                print(f"✅ 首次访问成功 (耗时: {end_time - start_time:.2f}秒)")
                print(f"   数据类型: {type(data1)}")
                
                if isinstance(data1, dict):
                    print(f"   数据结构: {list(data1.keys())}")
                    if 'data' in data1:
                        tokens = data1.get('data', [])
                        print(f"   获取到 {len(tokens)} 个代币数据")
                else:
                    print(f"   数据内容预览: {str(data1)[:200]}...")
            else:
                print("❌ 首次访问失败")
                return False
                
        except Exception as e:
            print(f"❌ 首次访问出错: {e}")
            return False
        
        # 步骤2: 检查cookies
        print("\n2️⃣ 检查保存的Cookies...")
        domain = get_domain_from_url(test_url)
        cookies = load_cookies_from_file(domain)
        
        if cookies:
            print(f"✅ 找到 {len(cookies)} 个cookies")
            print(f"   Cookies示例: {list(cookies.keys())[:3]}...")
        else:
            print("⚠️  没有找到保存的cookies")
        
        # 步骤3: 使用cookies进行后续调用
        print("\n3️⃣ 使用Cookies进行后续API调用...")
        
        # 测试相同的API
        print("   测试相同API...")
        start_time = time.time()
        data2 = fetch_with_cookies(test_url)
        end_time = time.time()
        
        if data2:
            print(f"   ✅ 相同API调用成功 (耗时: {end_time - start_time:.2f}秒)")
        else:
            print("   ❌ 相同API调用失败")
        
        # 测试不同的API端点
        print("   测试不同API端点...")
        different_url = "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens/sol?orderby=market_cap&direction=desc&limit=3"
        
        start_time = time.time()
        data3 = fetch_with_cookies(different_url)
        end_time = time.time()
        
        if data3:
            print(f"   ✅ 不同API调用成功 (耗时: {end_time - start_time:.2f}秒)")
        else:
            print("   ❌ 不同API调用失败")
        
        # 步骤4: 性能对比
        print("\n4️⃣ 性能对比测试...")
        
        # 多次使用cookies调用，测试稳定性
        success_count = 0
        total_time = 0
        test_count = 3
        
        for i in range(test_count):
            print(f"   第 {i+1} 次调用...")
            start_time = time.time()
            result = fetch_with_cookies(test_url)
            end_time = time.time()
            
            if result:
                success_count += 1
                total_time += (end_time - start_time)
                print(f"     ✅ 成功 (耗时: {end_time - start_time:.2f}秒)")
            else:
                print(f"     ❌ 失败")
            
            time.sleep(1)  # 避免请求过快
        
        if success_count > 0:
            avg_time = total_time / success_count
            print(f"\n   📊 性能统计:")
            print(f"     成功率: {success_count}/{test_count} ({success_count/test_count*100:.1f}%)")
            print(f"     平均耗时: {avg_time:.2f}秒")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        logging.error("Error during API testing", exc_info=True)
        return False
    finally:
        # 清理资源
        try:
            close_global_driver()
            print("\n🧹 浏览器资源已清理")
        except:
            pass

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===\n")
    
    try:
        from cloudflare_bypass_helper import fetch_with_cookies, close_global_driver
        
        # 测试无效URL
        print("1️⃣ 测试无效URL...")
        invalid_url = "https://invalid-domain-that-does-not-exist.com/api"
        result = fetch_with_cookies(invalid_url)
        
        if result is None:
            print("✅ 无效URL正确返回None")
        else:
            print("⚠️  无效URL未正确处理")
        
        # 测试网络超时（使用一个很慢的服务）
        print("\n2️⃣ 测试网络超时...")
        slow_url = "https://httpbin.org/delay/15"  # 15秒延迟，应该超时
        result = fetch_with_cookies(slow_url)
        
        if result is None:
            print("✅ 网络超时正确处理")
        else:
            print("⚠️  网络超时未正确处理")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False
    finally:
        try:
            close_global_driver()
        except:
            pass

def main():
    """主测试函数"""
    print("🚀 开始真实API调用测试\n")
    
    test_results = []
    
    # 运行测试
    print("⚠️  注意: 此测试需要网络连接，可能需要较长时间...")
    print("如果遇到Cloudflare验证，请在浏览器窗口中手动完成验证\n")
    
    test_results.append(("GMGN API和Cookies", test_gmgn_api_with_cookies()))
    test_results.append(("错误处理", test_error_handling()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 真实API测试结果汇总:")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print("-"*60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有真实API测试通过！")
        print("\n✨ 系统功能验证:")
        print("   ✅ Cloudflare绕过功能正常")
        print("   ✅ Cookies保存和重用功能正常") 
        print("   ✅ API调用性能优化有效")
        print("   ✅ 错误处理机制完善")
        return True
    else:
        print("⚠️  部分真实API测试失败")
        print("\n🔧 可能的原因:")
        print("   - 网络连接问题")
        print("   - Cloudflare防护策略变化")
        print("   - 目标网站API变更")
        print("   - 浏览器配置问题")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        try:
            from cloudflare_bypass_helper import close_global_driver
            close_global_driver()
        except:
            pass
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期的错误: {e}")
        logging.error("Unexpected error during real API testing", exc_info=True)
        try:
            from cloudflare_bypass_helper import close_global_driver
            close_global_driver()
        except:
            pass
        sys.exit(1)
