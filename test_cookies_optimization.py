#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试cookies优化方案
目标：一次绕过5秒，多个API复用cookies
"""

import requests
import json
import time
import logging
from datetime import datetime
from cloudflare_bypass_helper import fetch_page_data, load_cookies_from_file, get_domain_from_url

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 测试用的API URLs
TEST_URLS = [
    "https://gmgn.ai/defi/quotation/v1/tokens/sol",  # 常规代币API
    "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=100",  # 蓝筹代币API
]

class CookiesManager:
    def __init__(self):
        self.cookies_cache = {}
        self.cookies_status = {}
        self.last_bypass_time = {}
        
    def get_domain_status(self, domain):
        """获取域名的cookies状态"""
        if domain not in self.cookies_status:
            self.cookies_status[domain] = {
                'valid': False,
                'last_update': None,
                'retry_count': 0,
                'last_bypass': None
            }
        return self.cookies_status[domain]
    
    def is_cookies_valid(self, domain):
        """检查cookies是否有效"""
        status = self.get_domain_status(domain)
        
        # 如果从未绕过，肯定无效
        if not status['last_bypass']:
            return False
            
        # 如果最近绕过失败次数太多，认为无效
        if status['retry_count'] >= 3:
            return False
            
        # 如果cookies太旧（超过30分钟），认为可能无效
        if status['last_update']:
            time_diff = (datetime.now() - status['last_update']).total_seconds()
            if time_diff > 1800:  # 30分钟
                logging.warning(f"Cookies for {domain} are older than 30 minutes")
                return False
                
        return status['valid']
    
    def bypass_cloudflare_once(self, domain, target_url=None):
        """一次性绕过Cloudflare，获取有效cookies"""
        status = self.get_domain_status(domain)

        # 检查是否最近刚绕过（避免频繁绕过）
        if status['last_bypass']:
            time_since_bypass = (datetime.now() - status['last_bypass']).total_seconds()
            if time_since_bypass < 60:  # 1分钟内不重复绕过
                logging.info(f"Recently bypassed {domain}, skipping...")
                return status['valid']

        logging.info(f"🔄 开始绕过 {domain} 的Cloudflare保护...")

        # 使用目标URL进行绕过，确保cookies对该端点有效
        if target_url:
            test_url = target_url
        else:
            test_url = f"https://{domain}/api/v1/bluechip_rank/sol?limit=1"
        
        try:
            logging.info(f"使用URL进行绕过: {test_url}")
            result = fetch_page_data(test_url)
            
            if result:
                # 绕过成功，更新状态
                status['valid'] = True
                status['last_update'] = datetime.now()
                status['last_bypass'] = datetime.now()
                status['retry_count'] = 0
                
                # 加载新的cookies
                self.cookies_cache[domain] = load_cookies_from_file(domain)
                
                logging.info(f"✅ {domain} Cloudflare绕过成功")
                return True
            else:
                # 绕过失败
                status['retry_count'] += 1
                status['last_bypass'] = datetime.now()
                logging.error(f"❌ {domain} Cloudflare绕过失败")
                return False
                
        except Exception as e:
            status['retry_count'] += 1
            status['last_bypass'] = datetime.now()
            logging.error(f"❌ {domain} 绕过过程出错: {e}")
            return False
    
    def fetch_with_smart_cookies(self, url):
        """智能cookies请求"""
        domain = get_domain_from_url(url)
        
        # 1. 检查cookies是否有效
        if not self.is_cookies_valid(domain):
            logging.info(f"Cookies for {domain} invalid, need to bypass...")
            if not self.bypass_cloudflare_once(domain, target_url=url):
                logging.error(f"Failed to bypass {domain}")
                return None
        
        # 2. 使用cookies请求
        cookies = self.cookies_cache.get(domain) or load_cookies_from_file(domain)
        
        if not cookies:
            logging.error(f"No cookies available for {domain}")
            return None
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        try:
            logging.info(f"发送请求到: {url}")
            logging.info(f"使用cookies: {list(cookies.keys()) if cookies else 'None'}")

            response = requests.get(url, cookies=cookies, headers=headers, timeout=15)

            logging.info(f"响应状态码: {response.status_code}")
            logging.info(f"响应头: {dict(response.headers)}")

            if response.status_code == 200:
                # 检查是否被Cloudflare拦截
                if "Just a moment" in response.text or "Checking your browser" in response.text:
                    logging.warning(f"Cloudflare challenge detected for {url}")
                    # 标记cookies无效，但不立即重试
                    self.get_domain_status(domain)['valid'] = False
                    return None
                
                # 尝试解析JSON
                try:
                    if response.headers.get('content-type', '').startswith('application/json'):
                        return response.json()
                    else:
                        # HTML格式，尝试从<pre>标签提取JSON
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(response.text, 'html.parser')
                        pre_content = soup.find('pre')
                        if pre_content:
                            return json.loads(pre_content.text)
                        else:
                            logging.warning(f"No JSON data found in response from {url}")
                            return None
                except json.JSONDecodeError as e:
                    logging.error(f"JSON decode error for {url}: {e}")
                    return None
                    
            elif response.status_code == 403:
                logging.warning(f"403 Forbidden for {url}, cookies may be expired")
                # 标记cookies无效
                self.get_domain_status(domain)['valid'] = False
                return None
            else:
                logging.error(f"HTTP {response.status_code} for {url}")
                return None
                
        except Exception as e:
            logging.error(f"Request error for {url}: {e}")
            return None

def test_cookies_optimization():
    """测试cookies优化方案"""
    print("🧪 开始测试cookies优化方案")
    print("=" * 60)
    
    manager = CookiesManager()
    
    # 测试多个API调用
    for i, url in enumerate(TEST_URLS, 1):
        print(f"\n📋 测试 {i}/{len(TEST_URLS)}: {url}")
        print("-" * 40)
        
        start_time = time.time()
        result = manager.fetch_with_smart_cookies(url)
        end_time = time.time()
        
        if result:
            data_count = 0
            if isinstance(result, dict):
                if 'data' in result:
                    if isinstance(result['data'], list):
                        data_count = len(result['data'])
                    elif isinstance(result['data'], dict) and 'tokens' in result['data']:
                        data_count = len(result['data']['tokens'])
            
            print(f"✅ 成功获取数据，包含 {data_count} 条记录")
            print(f"⏱️  耗时: {end_time - start_time:.2f} 秒")
        else:
            print(f"❌ 获取失败")
            print(f"⏱️  耗时: {end_time - start_time:.2f} 秒")
        
        # 短暂延迟
        time.sleep(2)
    
    # 显示最终状态
    print(f"\n📊 最终状态:")
    print("-" * 40)
    for domain, status in manager.cookies_status.items():
        print(f"域名: {domain}")
        print(f"  有效: {status['valid']}")
        print(f"  重试次数: {status['retry_count']}")
        print(f"  最后更新: {status['last_update']}")
        print(f"  最后绕过: {status['last_bypass']}")

if __name__ == "__main__":
    test_cookies_optimization()
