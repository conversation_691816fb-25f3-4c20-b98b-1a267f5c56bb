#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试无头模式功能
"""

import logging
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_headless_mode():
    """测试无头模式"""
    print("=== 测试无头模式功能 ===\n")
    
    try:
        from cloudflare_bypass_helper import (
            set_headless_mode, 
            get_headless_mode,
            fetch_page_data, 
            close_global_driver
        )
        
        # 1. 测试设置无头模式
        print("1️⃣ 启用无头模式...")
        set_headless_mode(True)
        print(f"   当前无头模式状态: {get_headless_mode()}")
        
        # 2. 测试无头模式下的数据获取
        print("\n2️⃣ 无头模式下获取数据...")
        print("   注意: 浏览器窗口应该不可见")
        
        test_url = "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens/sol?limit=1"
        
        start_time = time.time()
        data = fetch_page_data(test_url)
        end_time = time.time()
        
        if data:
            print(f"   ✅ 无头模式数据获取成功 (耗时: {end_time - start_time:.2f}秒)")
            print(f"   数据类型: {type(data)}")
            if isinstance(data, dict):
                print(f"   数据键: {list(data.keys())}")
        else:
            print("   ❌ 无头模式数据获取失败")
        
        # 3. 清理资源
        close_global_driver()
        print("   ✅ 浏览器资源已清理")
        
        return data is not None
        
    except Exception as e:
        print(f"   ❌ 无头模式测试失败: {e}")
        logging.error("Headless mode test failed", exc_info=True)
        return False

def test_visible_mode():
    """测试可见模式"""
    print("\n=== 测试可见模式功能 ===\n")
    
    try:
        from cloudflare_bypass_helper import (
            set_headless_mode, 
            get_headless_mode,
            fetch_page_data, 
            close_global_driver
        )
        
        # 1. 测试设置可见模式
        print("1️⃣ 启用可见模式...")
        set_headless_mode(False)
        print(f"   当前无头模式状态: {get_headless_mode()}")
        
        # 2. 测试可见模式下的数据获取
        print("\n2️⃣ 可见模式下获取数据...")
        print("   注意: 浏览器窗口应该可见")
        print("   如果出现Cloudflare验证，请手动完成")
        
        test_url = "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens/sol?limit=1"
        
        start_time = time.time()
        data = fetch_page_data(test_url)
        end_time = time.time()
        
        if data:
            print(f"   ✅ 可见模式数据获取成功 (耗时: {end_time - start_time:.2f}秒)")
            print(f"   数据类型: {type(data)}")
        else:
            print("   ❌ 可见模式数据获取失败")
        
        # 3. 清理资源
        close_global_driver()
        print("   ✅ 浏览器资源已清理")
        
        return data is not None
        
    except Exception as e:
        print(f"   ❌ 可见模式测试失败: {e}")
        logging.error("Visible mode test failed", exc_info=True)
        return False

def compare_modes():
    """对比两种模式"""
    print("\n=== 模式对比 ===\n")
    
    print("📊 无头模式 vs 可见模式:")
    print("┌─────────────────┬─────────────┬─────────────┐")
    print("│ 特性            │ 无头模式    │ 可见模式    │")
    print("├─────────────────┼─────────────┼─────────────┤")
    print("│ 浏览器窗口      │ 不可见      │ 可见        │")
    print("│ 资源占用        │ 较低        │ 较高        │")
    print("│ 运行速度        │ 较快        │ 较慢        │")
    print("│ 调试便利性      │ 较难        │ 容易        │")
    print("│ 服务器部署      │ 适合        │ 不适合      │")
    print("│ 手动验证        │ 困难        │ 容易        │")
    print("└─────────────────┴─────────────┴─────────────┘")
    
    print("\n💡 使用建议:")
    print("   🖥️  开发调试时: 使用可见模式，方便观察和手动处理")
    print("   🚀 生产环境: 使用无头模式，节省资源和提高效率")
    print("   ⚡ 批量处理: 使用无头模式，避免界面干扰")

def main():
    """主测试函数"""
    print("🚀 测试浏览器无头模式功能\n")
    
    # 询问用户想要测试哪种模式
    print("请选择测试模式:")
    print("1. 无头模式 (浏览器不可见)")
    print("2. 可见模式 (浏览器可见)")
    print("3. 两种模式都测试")
    print("4. 只显示对比信息")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        results = []
        
        if choice == "1":
            results.append(("无头模式", test_headless_mode()))
        elif choice == "2":
            results.append(("可见模式", test_visible_mode()))
        elif choice == "3":
            results.append(("无头模式", test_headless_mode()))
            results.append(("可见模式", test_visible_mode()))
        elif choice == "4":
            compare_modes()
            return True
        else:
            print("无效选择，默认测试无头模式")
            results.append(("无头模式", test_headless_mode()))
        
        # 显示对比信息
        compare_modes()
        
        # 汇总结果
        if results:
            print("\n" + "="*50)
            print("📊 测试结果汇总:")
            print("="*50)
            
            passed = 0
            total = len(results)
            
            for test_name, result in results:
                status = "✅ 成功" if result else "❌ 失败"
                print(f"{test_name:15} : {status}")
                if result:
                    passed += 1
            
            print("-"*50)
            print(f"总计: {passed}/{total} 项测试通过")
            
            if passed > 0:
                print("\n🎉 无头模式功能测试成功！")
                return True
            else:
                print("\n⚠️  无头模式功能需要调试")
                return False
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logging.error("Main test failed", exc_info=True)
        return False
    finally:
        # 确保清理资源
        try:
            from cloudflare_bypass_helper import close_global_driver
            close_global_driver()
        except:
            pass

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 程序发生未预期的错误: {e}")
        logging.error("Unexpected error", exc_info=True)
        exit(1)
