#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接测试TelegramBot类
"""

from TelegramBot import TelegramBot
import asyncio
import threading

def test_callback(message):
    print(f"[回调成功] {message}")

def main():
    print("🔍 直接测试TelegramBot类...")
    
    # 创建机器人实例
    BOT_TOKEN = "7616525518:AAGWbdc9y5pu9o9UVFkOEnhXN7Yk8TJVY-E"
    bot = TelegramBot(BOT_TOKEN)
    
    print(f"[DEBUG] 机器人创建完成")
    print(f"[DEBUG] gui_callback属性存在: {hasattr(bot, 'gui_callback')}")
    
    # 设置回调
    bot.gui_callback = test_callback
    print(f"[DEBUG] 回调设置完成: {bot.gui_callback}")
    
    # 在新线程中运行机器人
    def run_bot():
        try:
            # 为新线程创建事件循环
            asyncio.set_event_loop(asyncio.new_event_loop())
            print(f"[DEBUG] 开始运行机器人...")
            bot.application.run_polling()
        except Exception as e:
            print(f"[ERROR] 机器人运行错误: {e}")
    
    bot_thread = threading.Thread(target=run_bot, daemon=True)
    bot_thread.start()
    
    print("✅ 机器人已启动，请在Telegram中发送 /test 命令")
    print("按Ctrl+C退出")
    
    try:
        # 保持主线程运行
        while True:
            import time
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n程序已退出")

if __name__ == "__main__":
    main()
