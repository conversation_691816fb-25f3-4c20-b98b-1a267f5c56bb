#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI版本打包脚本
"""

import os
import sys
import subprocess
from PIL import Image

def convert_jpg_to_ico():
    """将JPG图片转换为ICO格式"""
    try:
        # 输入和输出路径
        jpg_path = os.path.join("img", "狗头.jpg")
        ico_path = os.path.join("img", "icon.ico")
        
        if not os.path.exists(jpg_path):
            print(f"❌ 图片文件不存在: {jpg_path}")
            return None
        
        # 打开图片并转换
        img = Image.open(jpg_path)
        
        # 创建多种尺寸的图标
        icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        
        # 保存为ICO格式
        img.save(ico_path, format='ICO', sizes=icon_sizes)
        
        print(f"✅ 图标转换成功: {ico_path}")
        return ico_path
        
    except ImportError:
        print("❌ PIL库未安装，无法转换图标")
        print("请运行: pip install Pillow")
        return None
    except Exception as e:
        print(f"❌ 图标转换失败: {e}")
        return None

def create_spec_file(icon_path=None):
    """创建PyInstaller spec文件"""
    
    # 基础配置
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('img', 'img'),
        ('cloudflare_bypass_helper.py', '.'),
        ('gmgn_fetcher.py', '.'),
        ('TelegramBot.py', '.'),
        ('CloudflareBypasser.py', '.'),
    ],
    hiddenimports=[
        'telegram',
        'telegram.ext',
        'DrissionPage',
        'beautifulsoup4',
        'requests',
        'schedule',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SolanaTokenBot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
'''

    # 添加图标配置
    if icon_path and os.path.exists(icon_path):
        spec_content += f"    icon='{icon_path}',\n"
    
    spec_content += ")\n"
    
    # 写入spec文件
    with open('main_gui.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Spec文件创建成功: main_gui.spec")

def build_executable():
    """构建可执行文件"""
    try:
        print("🚀 开始构建可执行文件...")
        
        # 运行PyInstaller
        cmd = [sys.executable, '-m', 'PyInstaller', 'main_gui.spec', '--clean']
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print("📁 可执行文件位置: dist/SolanaTokenBot.exe")
            return True
        else:
            print("❌ 构建失败！")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'PyInstaller',
        'Pillow',
        'python-telegram-bot',
        'DrissionPage',
        'beautifulsoup4',
        'requests',
        'schedule'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'python-telegram-bot':
                import telegram
            elif package == 'Pillow':
                import PIL
            elif package == 'beautifulsoup4':
                import bs4
            elif package == 'DrissionPage':
                import DrissionPage
            elif package == 'PyInstaller':
                import PyInstaller
            else:
                __import__(package.lower())
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        for package in missing_packages:
            print(f"   pip install {package}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def main():
    """主函数"""
    print("🎯 Solana代币机器人GUI版本打包工具")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    print("\n📦 开始打包流程...")
    
    # 1. 转换图标
    print("\n1️⃣ 转换图标...")
    icon_path = convert_jpg_to_ico()
    
    # 2. 创建spec文件
    print("\n2️⃣ 创建配置文件...")
    create_spec_file(icon_path)
    
    # 3. 构建可执行文件
    print("\n3️⃣ 构建可执行文件...")
    success = build_executable()
    
    # 4. 结果报告
    print("\n" + "="*50)
    if success:
        print("🎉 打包完成！")
        print("\n📁 生成的文件:")
        print("   - dist/SolanaTokenBot.exe (主程序)")
        print("   - img/icon.ico (图标文件)")
        print("   - main_gui.spec (配置文件)")
        
        print("\n✨ 程序特性:")
        print("   🖼️  自定义图标 (狗头.jpg)")
        print("   🔇 无头模式 (浏览器不可见)")
        print("   📊 实时状态显示")
        print("   📝 运行日志")
        print("   🎛️  图形界面控制")
        print("   ❌ 一键关闭")
        
        print("\n🚀 使用方法:")
        print("   1. 双击 SolanaTokenBot.exe 启动")
        print("   2. 点击'启动机器人'按钮")
        print("   3. 在Telegram中使用 /JK 等命令")
        print("   4. 观察GUI界面的状态和日志")
        print("   5. 点击'关闭程序'安全退出")
        
    else:
        print("❌ 打包失败！")
        print("请检查错误信息并重试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
