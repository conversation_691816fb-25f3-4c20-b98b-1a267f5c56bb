#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
精确模拟浏览器请求头的测试
"""

import requests
import json
import logging
from cloudflare_bypass_helper import fetch_page_data, load_cookies_from_file

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_exact_browser_request():
    """使用精确的浏览器请求头测试"""
    print("🧪 测试精确的浏览器请求头")
    print("=" * 60)
    
    # 测试URL
    url = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10"
    
    # 1. 先绕过Cloudflare获取cookies
    print("🔄 绕过Cloudflare获取cookies...")
    result = fetch_page_data(url)
    
    if not result:
        print("❌ Cloudflare绕过失败")
        return
    
    # 2. 加载cookies
    cookies = load_cookies_from_file('gmgn.ai')
    if not cookies:
        print("❌ 无法加载cookies")
        return
    
    print(f"✅ 加载了 {len(cookies)} 个cookies: {list(cookies.keys())}")
    
    # 3. 使用精确的浏览器请求头
    headers = {
        # 基础头部
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'max-age=0',
        'priority': 'u=0, i',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'upgrade-insecure-requests': '1',
        
        # Sec-CH-UA 系列（Chrome特有）
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-arch': '"x86"',
        'sec-ch-ua-bitness': '"64"',
        'sec-ch-ua-full-version': '"137.0.7151.120"',
        'sec-ch-ua-full-version-list': '"Google Chrome";v="137.0.7151.120", "Chromium";v="137.0.7151.120", "Not/A)Brand";v="********"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-model': '""',
        'sec-ch-ua-platform': '"Windows"',
        'sec-ch-ua-platform-version': '"15.0.0"',
        
        # Sec-Fetch 系列（安全策略）
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
    }
    
    print("\n🧪 发送精确模拟的浏览器请求...")
    
    try:
        response = requests.get(url, cookies=cookies, headers=headers, timeout=15)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应Content-Type: {response.headers.get('content-type', 'unknown')}")
        print(f"响应Content-Encoding: {response.headers.get('content-encoding', 'none')}")
        print(f"响应长度: {len(response.content)} bytes")
        
        if response.status_code == 200:
            # 检查内容类型
            content_type = response.headers.get('content-type', '')
            
            if 'application/json' in content_type:
                # JSON响应
                try:
                    data = response.json()
                    if isinstance(data, dict) and 'data' in data:
                        count = len(data['data']) if isinstance(data['data'], list) else 1
                        print(f"✅ 成功获取JSON数据，包含 {count} 条记录")
                        
                        # 显示第一条记录的基本信息
                        if isinstance(data['data'], list) and len(data['data']) > 0:
                            first_token = data['data'][0]
                            symbol = first_token.get('symbol', 'Unknown')
                            price = first_token.get('price', 0)
                            volume = first_token.get('volume', 0)
                            print(f"第一条记录: {symbol}, 价格: {price}, 交易量: {volume}")
                        
                        return True
                    else:
                        print(f"⚠️ JSON格式异常: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                        return False
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    return False
            else:
                # HTML响应，尝试提取JSON
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                pre_content = soup.find('pre')
                if pre_content:
                    try:
                        data = json.loads(pre_content.text)
                        if isinstance(data, dict) and 'data' in data:
                            count = len(data['data']) if isinstance(data['data'], list) else 1
                            print(f"✅ 从HTML提取JSON数据，包含 {count} 条记录")
                            return True
                    except json.JSONDecodeError:
                        print("❌ HTML中的JSON解析失败")
                        return False
                else:
                    print("❌ HTML中未找到JSON数据")
                    # 保存HTML用于调试
                    with open('debug_success_response.html', 'w', encoding='utf-8') as f:
                        f.write(response.text[:2000])
                    print("响应已保存到 debug_success_response.html")
                    return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
            # 解压缩并保存错误响应
            try:
                if response.headers.get('content-encoding') == 'br':
                    import brotli
                    decompressed = brotli.decompress(response.content)
                    error_text = decompressed.decode('utf-8')
                elif response.headers.get('content-encoding') == 'gzip':
                    import gzip
                    decompressed = gzip.decompress(response.content)
                    error_text = decompressed.decode('utf-8')
                else:
                    error_text = response.text
                
                with open('debug_error_exact.txt', 'w', encoding='utf-8') as f:
                    f.write(error_text[:2000])
                print("错误响应已保存到 debug_error_exact.txt")
                
                # 检查是否是Cloudflare错误
                if "cloudflare" in error_text.lower() or "ray id" in error_text.lower():
                    print("⚠️ 这是Cloudflare错误页面")
                elif "403" in error_text or "forbidden" in error_text.lower():
                    print("⚠️ 这是403禁止访问错误")
                
            except Exception as e:
                print(f"解压缩错误响应失败: {e}")
            
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def compare_cookies():
    """比较我们的cookies和浏览器cookies"""
    print("\n🔍 比较cookies...")
    print("-" * 40)
    
    # 我们的cookies
    our_cookies = load_cookies_from_file('gmgn.ai')
    if our_cookies:
        print("我们的cookies:")
        for name, value in our_cookies.items():
            print(f"  {name}: {value[:50]}...")
    
    # 浏览器cookies（从F12复制）
    browser_cookies_str = "cf_clearance=wzczcNG5v16ZOs4KrL_2Bl7ZSYd3ot6Xo1yOPb.KybI-1751945758-1.2.1.1-jTDYFHXFKJjHOpDD31nopL7KTTcbhpdMpExyv2S3Wbs1c0JYGJjODWzClTI6BjpQ5sPfmyV.JR2rHLR4DaFjqALBjeAcoxlHE07qZBX9Y1nIDExNs57MC4vD.SRNfXmqRW4VZ6MqqMUgaH0LvAfcHiy6hQbME.02VojbXc6aG3.CJQKcGkGlHn7Yr.JlBqz6DPSBXD1RmUAXGyYon326Z8r6yLf9cSl1knMWfz9wpOA; __cf_bm=khnbWP_VRVATuxe_mNNd9nAgYHbJagU.mOiC9t8_gvE-1752030276-1.0.1.1-oosJ1nJLcsnfHl2ffrsf_QTQGqniNcANEKrkMQvqT2_NtFxEZPpDQK.FPjyfU6kEWHiK134zUzMZ9ShiEUVcB6HtmlaIAfQEIufKB9OM4EE"
    
    print("\n浏览器cookies:")
    for cookie_pair in browser_cookies_str.split('; '):
        if '=' in cookie_pair:
            name, value = cookie_pair.split('=', 1)
            print(f"  {name}: {value[:50]}...")
    
    # 检查差异
    print("\n差异分析:")
    if our_cookies:
        our_cf_clearance = our_cookies.get('cf_clearance', '')
        browser_cf_clearance = browser_cookies_str.split('cf_clearance=')[1].split(';')[0] if 'cf_clearance=' in browser_cookies_str else ''
        
        if our_cf_clearance != browser_cf_clearance:
            print("⚠️ cf_clearance cookies不同")
            print(f"  我们的: {our_cf_clearance[:50]}...")
            print(f"  浏览器: {browser_cf_clearance[:50]}...")
        else:
            print("✅ cf_clearance cookies相同")

if __name__ == "__main__":
    # 比较cookies
    compare_cookies()
    
    # 测试精确请求
    success = test_exact_browser_request()
    
    if success:
        print("\n🎉 成功！精确模拟浏览器请求有效")
    else:
        print("\n❌ 失败！需要进一步调试")
