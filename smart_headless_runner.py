#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能无头模式运行器 - 自动处理无头模式失败的情况
"""

import logging
import time
import sys
from datetime import datetime
from cloudflare_bypass_helper import (
    set_headless_mode, 
    get_headless_mode,
    fetch_page_data, 
    fetch_with_cookies,
    close_global_driver,
    load_cookies_from_file,
    get_domain_from_url
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SmartHeadlessRunner:
    def __init__(self):
        self.prefer_headless = True
        self.fallback_to_visible = True
        self.cookies_established = False

    def smart_fetch_data(self, url, max_retries=2):
        """
        智能获取数据 - 优先使用无头模式，失败时回退到可见模式
        """
        print(f"🎯 智能获取数据: {url[:60]}...")
        
        # 首先检查是否有有效的cookies
        domain = get_domain_from_url(url)
        cached_cookies = load_cookies_from_file(domain)
        
        if cached_cookies:
            print("   📦 发现缓存cookies，尝试直接使用...")
            try:
                # 使用无头模式尝试cookies请求
                set_headless_mode(True)
                data = fetch_with_cookies(url)
                if data:
                    print("   ✅ 使用缓存cookies成功")
                    return data
                else:
                    print("   ⚠️  缓存cookies可能已过期")
            except Exception as e:
                print(f"   ⚠️  使用缓存cookies失败: {e}")
        
        # 如果cookies无效，需要重新绕过Cloudflare
        for attempt in range(max_retries):
            try:
                if attempt == 0 and self.prefer_headless:
                    # 第一次尝试：无头模式
                    print(f"   🔇 尝试 {attempt + 1}: 无头模式")
                    set_headless_mode(True)
                    data = fetch_page_data(url, use_cached_cookies=False)
                    
                    if data:
                        print("   ✅ 无头模式成功")
                        self.cookies_established = True
                        return data
                    else:
                        print("   ❌ 无头模式失败")
                        
                elif self.fallback_to_visible:
                    # 回退到可见模式
                    print(f"   🖥️  尝试 {attempt + 1}: 可见模式")
                    set_headless_mode(False)
                    data = fetch_page_data(url, use_cached_cookies=False)
                    
                    if data:
                        print("   ✅ 可见模式成功")
                        self.cookies_established = True
                        
                        # 成功后切换回无头模式供后续使用
                        if self.prefer_headless:
                            print("   🔄 切换回无头模式供后续使用")
                            set_headless_mode(True)
                        
                        return data
                    else:
                        print("   ❌ 可见模式也失败")
                
                # 短暂延迟后重试
                if attempt < max_retries - 1:
                    print("   ⏳ 等待5秒后重试...")
                    time.sleep(5)
                    
            except Exception as e:
                print(f"   ❌ 尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
        
        print("   ❌ 所有尝试都失败了")
        return None

    def run_analysis_workflow(self):
        """运行完整的分析工作流"""
        print("🚀 智能无头模式分析工作流")
        print("="*50)
        
        try:
            # 测试URLs
            test_urls = [
                "https://gmgn.ai/defi/quotation/v1/tokens/top_tokens/sol?limit=3",
                "https://gmgn.ai/api/v1/bluechip_rank/sol?limit=3"
            ]
            
            results = []
            
            for i, url in enumerate(test_urls, 1):
                print(f"\n{i}️⃣ 测试API {i}:")
                
                start_time = time.time()
                data = self.smart_fetch_data(url)
                end_time = time.time()
                
                if data:
                    print(f"   ✅ 成功 (耗时: {end_time - start_time:.2f}秒)")
                    print(f"   📊 数据类型: {type(data)}")
                    if isinstance(data, dict):
                        print(f"   🔑 数据键: {list(data.keys())[:3]}...")
                    results.append(True)
                else:
                    print(f"   ❌ 失败 (耗时: {end_time - start_time:.2f}秒)")
                    results.append(False)
                
                # 短暂延迟
                time.sleep(2)
            
            # 统计结果
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 工作流结果:")
            print(f"   成功: {success_count}/{total_count}")
            print(f"   成功率: {success_count/total_count*100:.1f}%")
            print(f"   Cookies状态: {'✅ 已建立' if self.cookies_established else '❌ 未建立'}")
            print(f"   当前模式: {'🔇 无头模式' if get_headless_mode() else '🖥️ 可见模式'}")
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 工作流执行失败: {e}")
            logging.error("Workflow failed", exc_info=True)
            return False
        finally:
            close_global_driver()
            print("\n🧹 浏览器资源已清理")

def demonstrate_smart_headless():
    """演示智能无头模式"""
    print("🎯 智能无头模式演示")
    print("="*40)
    
    print("\n💡 智能无头模式特点:")
    print("   1. 优先使用无头模式（更快、更省资源）")
    print("   2. 无头模式失败时自动切换到可见模式")
    print("   3. 建立cookies后切换回无头模式")
    print("   4. 智能重试机制")
    print("   5. 自动资源清理")
    
    print("\n🔄 工作流程:")
    print("   1. 检查缓存cookies → 2. 尝试无头模式 → 3. 回退可见模式 → 4. 切换回无头模式")
    
    # 询问是否运行演示
    try:
        choice = input("\n是否运行智能无头模式演示？[y/N]: ").strip().lower()
        if choice in ['y', 'yes', '是']:
            runner = SmartHeadlessRunner()
            success = runner.run_analysis_workflow()
            
            if success:
                print("\n🎉 智能无头模式演示成功！")
                print("\n✨ 优势总结:")
                print("   ✅ 自动处理无头模式限制")
                print("   ✅ 无需手动干预")
                print("   ✅ 最大化性能和兼容性")
                print("   ✅ 适合生产环境使用")
            else:
                print("\n⚠️  演示未完全成功，但机制正常工作")
            
            return success
        else:
            print("演示已跳过")
            return True
            
    except KeyboardInterrupt:
        print("\n演示被中断")
        return False

def main():
    """主函数"""
    try:
        success = demonstrate_smart_headless()
        
        print("\n" + "="*50)
        print("📋 使用建议:")
        print("="*50)
        
        print("\n🔧 集成到您的项目:")
        print("```python")
        print("from smart_headless_runner import SmartHeadlessRunner")
        print("")
        print("runner = SmartHeadlessRunner()")
        print("data = runner.smart_fetch_data(your_url)")
        print("```")
        
        print("\n🚀 生产环境使用:")
        print("   1. 将 SmartHeadlessRunner 集成到现有代码")
        print("   2. 替换直接的 fetch_page_data 调用")
        print("   3. 享受自动化的无头/可见模式切换")
        print("   4. 无需担心Cloudflare验证问题")
        
        return success
        
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        logging.error("Main function failed", exc_info=True)
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序发生未预期的错误: {e}")
        logging.error("Unexpected error", exc_info=True)
        sys.exit(1)
