#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行完整的代币筛选和分析流程，但不推送到Telegram
"""

import logging
import json
import time
from datetime import datetime
from collections import OrderedDict
from cloudflare_bypass_helper import fetch_page_data, fetch_with_cookies, close_global_driver, set_headless_mode
from gmgn_fetcher import (
    filter_tokens, 
    format_tokens, 
    filter_bluechip_tokens, 
    format_bluechip_tokens, 
    format_bluechip_tokens_with_price_info,
    ca_format_tg_message
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# API URLs
GMGN_API_URL = "https://gmgn.ai/defi/quotation/v1/tokens/sol"
BLUECHIP_API_URL = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=100"

class TokenAnalyzer:
    def __init__(self):
        self.sent_tokens_cache = OrderedDict()
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'regular_tokens': [],
            'bluechip_tokens': [],
            'new_tokens': [],
            'price_update_tokens': [],
            'statistics': {}
        }

    def run_regular_token_analysis(self):
        """运行常规代币分析"""
        print("🔍 开始常规代币分析...")
        
        try:
            # 获取代币数据
            print("   获取代币数据...")
            tokens_data = fetch_page_data(GMGN_API_URL)
            
            if not tokens_data or "data" not in tokens_data or "tokens" not in tokens_data["data"]:
                print("   ❌ 未能获取代币数据")
                return False
            
            tokens = tokens_data["data"]["tokens"]
            print(f"   ✅ 获取到 {len(tokens)} 个代币")
            
            # 筛选代币
            print("   筛选代币...")
            filtered_tokens = filter_tokens(
                tokens,
                min_liquidity=120000,
                min_volume=200000,
                max_holders_rate=0.2
            )
            
            print(f"   ✅ 筛选出 {len(filtered_tokens)} 个符合条件的代币")
            
            # 格式化代币信息
            if filtered_tokens:
                print("   格式化代币信息...")
                formatted_messages = format_tokens(filtered_tokens)
                
                self.results['regular_tokens'] = filtered_tokens
                self.results['statistics']['regular_tokens_count'] = len(filtered_tokens)
                
                # 保存到文件
                with open('regular_tokens_analysis.txt', 'w', encoding='utf-8') as f:
                    f.write(f"常规代币分析结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("="*60 + "\n\n")
                    f.write(formatted_messages)
                
                print(f"   ✅ 结果已保存到 regular_tokens_analysis.txt")
                
                # 显示前3个代币的简要信息
                print("\n   📋 前3个代币预览:")
                for i, token in enumerate(filtered_tokens[:3]):
                    symbol = token.get('symbol', 'Unknown')
                    address = token.get('address', 'No address')
                    price = token.get('price', 0)
                    volume = token.get('volume_24h', 0)
                    print(f"      {i+1}. {symbol} - 价格: ${price:.6f} - 24h交易量: ${volume:,.0f}")
            else:
                print("   ⚠️  没有符合条件的常规代币")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 常规代币分析失败: {e}")
            logging.error("Regular token analysis failed", exc_info=True)
            return False

    def run_bluechip_token_analysis(self):
        """运行蓝筹代币分析"""
        print("\n💎 开始蓝筹代币分析...")
        
        try:
            # 获取蓝筹代币数据
            print("   获取蓝筹代币数据...")
            response = fetch_with_cookies(BLUECHIP_API_URL)
            
            if not response or "data" not in response:
                print("   ❌ 未能获取蓝筹代币数据")
                return False
            
            tokens = response["data"]
            print(f"   ✅ 获取到 {len(tokens)} 个蓝筹代币")
            
            # 筛选蓝筹代币
            print("   筛选蓝筹代币...")
            filtered_tokens = filter_bluechip_tokens(tokens, min_volume=20_000)
            print(f"   ✅ 筛选出 {len(filtered_tokens)} 个符合条件的蓝筹代币")
            
            # 分析新代币和价格更新
            print("   分析新代币和价格变化...")
            tokens_to_process = []
            new_tokens = []
            price_update_tokens = []
            
            for token in filtered_tokens:
                address = token['address']
                current_price = float(token.get('price', 0))
                
                if address not in self.sent_tokens_cache:
                    # 新代币
                    token_info = {
                        'token': token,
                        'is_price_update': False,
                        'price_multiplier': None,
                        'original_price': current_price
                    }
                    tokens_to_process.append(token_info)
                    new_tokens.append(token)
                    
                    self.sent_tokens_cache[address] = {
                        'price': current_price,
                        'timestamp': datetime.now().isoformat(),
                        'last_notified_multiplier': 1.0
                    }
                else:
                    # 检查价格变化
                    cached_info = self.sent_tokens_cache[address]
                    if isinstance(cached_info, dict) and 'price' in cached_info:
                        original_price = cached_info['price']
                        last_notified_multiplier = cached_info.get('last_notified_multiplier', 1.0)
                        
                        if original_price > 0:
                            current_multiplier = current_price / original_price
                            next_threshold = last_notified_multiplier * 2
                            
                            if current_multiplier >= next_threshold:
                                # 价格翻倍
                                token_info = {
                                    'token': token,
                                    'is_price_update': True,
                                    'price_multiplier': current_multiplier,
                                    'original_price': original_price
                                }
                                tokens_to_process.append(token_info)
                                price_update_tokens.append(token)
                                
                                self.sent_tokens_cache[address]['last_notified_multiplier'] = current_multiplier
                                self.sent_tokens_cache[address]['last_update_timestamp'] = datetime.now().isoformat()
            
            print(f"   ✅ 发现 {len(new_tokens)} 个新代币")
            print(f"   ✅ 发现 {len(price_update_tokens)} 个价格翻倍代币")
            
            # 保存结果
            self.results['bluechip_tokens'] = filtered_tokens
            self.results['new_tokens'] = new_tokens
            self.results['price_update_tokens'] = price_update_tokens
            self.results['statistics']['bluechip_tokens_count'] = len(filtered_tokens)
            self.results['statistics']['new_tokens_count'] = len(new_tokens)
            self.results['statistics']['price_update_tokens_count'] = len(price_update_tokens)
            
            # 格式化并保存结果
            if tokens_to_process:
                print("   格式化蓝筹代币信息...")
                formatted_messages = format_bluechip_tokens_with_price_info(tokens_to_process)
                
                with open('bluechip_tokens_analysis.txt', 'w', encoding='utf-8') as f:
                    f.write(f"蓝筹代币分析结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("="*60 + "\n\n")
                    for i, message in enumerate(formatted_messages, 1):
                        f.write(f"代币 {i}:\n")
                        f.write(message)
                        f.write("\n" + "-"*40 + "\n\n")
                
                print(f"   ✅ 结果已保存到 bluechip_tokens_analysis.txt")
                
                # 显示简要统计
                print(f"\n   📊 蓝筹代币统计:")
                print(f"      总数: {len(filtered_tokens)}")
                print(f"      新代币: {len(new_tokens)}")
                print(f"      价格翻倍: {len(price_update_tokens)}")
                
                # 显示新代币预览
                if new_tokens:
                    print(f"\n   🆕 新代币预览 (前3个):")
                    for i, token in enumerate(new_tokens[:3]):
                        symbol = token.get('symbol', 'Unknown')
                        price = float(token.get('price', 0)) if token.get('price') else 0
                        volume = float(token.get('volume', 0)) if token.get('volume') else 0
                        print(f"      {i+1}. {symbol} - 价格: ${price:.6f} - 交易量: ${volume:,.0f}")
                
                # 显示价格翻倍代币预览
                if price_update_tokens:
                    print(f"\n   📈 价格翻倍代币预览:")
                    for i, token in enumerate(price_update_tokens[:3]):
                        symbol = token.get('symbol', 'Unknown')
                        address = token.get('address', '')
                        current_price = float(token.get('price', 0))
                        if address in self.sent_tokens_cache:
                            original_price = self.sent_tokens_cache[address]['price']
                            multiplier = current_price / original_price if original_price > 0 else 0
                            print(f"      {i+1}. {symbol} - 涨幅: {multiplier:.2f}x")
            else:
                print("   ⚠️  没有需要处理的蓝筹代币")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 蓝筹代币分析失败: {e}")
            logging.error("Bluechip token analysis failed", exc_info=True)
            return False

    def analyze_specific_token(self, token_address):
        """分析特定代币"""
        print(f"\n🔎 分析特定代币: {token_address}")
        
        try:
            # 获取代币详细信息
            url = f"https://gmgn.ai/_next/data/_pLFFWLu7OQ0hfgtjCi3I/sol/token/{token_address}.json?chain=sol&token={token_address}"
            response = fetch_with_cookies(url)
            
            if response and "pageProps" in response:
                token_info = response["pageProps"].get("tokenInfo", {})
                
                # 格式化信息
                message = ca_format_tg_message(token_info)
                
                if message:
                    print("   ✅ 代币信息获取成功")
                    
                    # 保存到文件
                    with open(f'token_analysis_{token_address}.txt', 'w', encoding='utf-8') as f:
                        f.write(f"代币分析结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("="*60 + "\n\n")
                        f.write(message)
                    
                    print(f"   ✅ 结果已保存到 token_analysis_{token_address}.txt")
                    
                    # 显示简要信息
                    symbol = token_info.get('symbol', 'Unknown')
                    name = token_info.get('name', 'Unknown')
                    price = token_info.get('price', 'Unknown')
                    print(f"   📋 {symbol} ({name}) - 价格: {price}")
                    
                    return True
                else:
                    print("   ❌ 无法格式化代币信息")
                    return False
            else:
                print("   ❌ 未能获取代币信息")
                return False
                
        except Exception as e:
            print(f"   ❌ 特定代币分析失败: {e}")
            logging.error("Specific token analysis failed", exc_info=True)
            return False

    def save_summary_report(self):
        """保存总结报告"""
        print("\n📄 生成总结报告...")
        
        try:
            # 保存JSON格式的详细数据
            with open('token_analysis_summary.json', 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
            
            # 生成可读的总结报告
            with open('token_analysis_summary.txt', 'w', encoding='utf-8') as f:
                f.write(f"代币分析总结报告\n")
                f.write(f"生成时间: {self.results['timestamp']}\n")
                f.write("="*60 + "\n\n")
                
                f.write("📊 统计信息:\n")
                stats = self.results['statistics']
                f.write(f"   常规代币数量: {stats.get('regular_tokens_count', 0)}\n")
                f.write(f"   蓝筹代币数量: {stats.get('bluechip_tokens_count', 0)}\n")
                f.write(f"   新发现代币: {stats.get('new_tokens_count', 0)}\n")
                f.write(f"   价格翻倍代币: {stats.get('price_update_tokens_count', 0)}\n\n")
                
                f.write("📁 生成的文件:\n")
                f.write("   - regular_tokens_analysis.txt (常规代币详情)\n")
                f.write("   - bluechip_tokens_analysis.txt (蓝筹代币详情)\n")
                f.write("   - token_analysis_summary.json (完整数据)\n")
                f.write("   - token_analysis_summary.txt (本报告)\n")
            
            print("   ✅ 总结报告已保存")
            return True
            
        except Exception as e:
            print(f"   ❌ 保存总结报告失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 开始运行完整的代币分析流程 (不推送TG)\n")

    # 询问是否使用无头模式
    try:
        headless_choice = input("是否使用无头模式？(浏览器不可见) [y/N]: ").strip().lower()
        if headless_choice in ['y', 'yes', '是']:
            set_headless_mode(True)
            print("✅ 已启用无头模式 (浏览器窗口不可见)")
        else:
            set_headless_mode(False)
            print("✅ 已启用可见模式 (浏览器窗口可见)")
    except KeyboardInterrupt:
        print("\n程序被中断")
        return False
    except:
        # 如果无法获取用户输入（如在脚本中运行），默认使用无头模式
        set_headless_mode(True)
        print("✅ 默认启用无头模式 (浏览器窗口不可见)")

    print(f"\n开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    analyzer = TokenAnalyzer()
    
    try:
        # 运行分析流程
        results = []
        
        # 1. 常规代币分析
        results.append(("常规代币分析", analyzer.run_regular_token_analysis()))
        
        # 2. 蓝筹代币分析
        results.append(("蓝筹代币分析", analyzer.run_bluechip_token_analysis()))
        
        # 3. 生成总结报告
        results.append(("总结报告", analyzer.save_summary_report()))
        
        # 显示结果
        print("\n" + "="*60)
        print("📊 分析结果汇总:")
        print("="*60)
        
        passed = 0
        total = len(results)
        
        for task_name, result in results:
            status = "✅ 成功" if result else "❌ 失败"
            print(f"{task_name:15} : {status}")
            if result:
                passed += 1
        
        print("-"*60)
        print(f"总计: {passed}/{total} 项任务完成")
        
        if passed >= total * 0.8:
            print("\n🎉 代币分析流程成功完成！")
            print("\n📁 生成的文件:")
            print("   - regular_tokens_analysis.txt")
            print("   - bluechip_tokens_analysis.txt") 
            print("   - token_analysis_summary.json")
            print("   - token_analysis_summary.txt")
            
            print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            return True
        else:
            print("\n⚠️  部分分析任务失败，请检查日志")
            return False
            
    except Exception as e:
        print(f"\n❌ 分析流程发生错误: {e}")
        logging.error("Main analysis process failed", exc_info=True)
        return False
    finally:
        # 清理资源
        try:
            close_global_driver()
            print("\n🧹 浏览器资源已清理")
        except:
            pass

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  分析流程被用户中断")
        try:
            close_global_driver()
        except:
            pass
        exit(1)
    except Exception as e:
        print(f"\n❌ 程序发生未预期的错误: {e}")
        logging.error("Unexpected error in main", exc_info=True)
        try:
            close_global_driver()
        except:
            pass
        exit(1)
