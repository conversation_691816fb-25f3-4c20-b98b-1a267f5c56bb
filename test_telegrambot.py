#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试TelegramBot类的回调机制
"""

from TelegramBot import TelegramBot
import sys

def test_callback(message):
    print(f"[GUI回调] {message}", flush=True)
    sys.stdout.flush()

def main():
    print("🚀 测试TelegramBot类回调机制...", flush=True)
    print("机器人用户名: @solzjf_bot", flush=True)
    print("可以发送: /start, /test, /pushing, /JK, /ca", flush=True)
    print("按Ctrl+C退出", flush=True)
    
    # 创建机器人实例
    BOT_TOKEN = "7616525518:AAGWbdc9y5pu9o9UVFkOEnhXN7Yk8TJVY-E"
    bot = TelegramBot(BOT_TOKEN)
    
    print(f"[DEBUG] 机器人创建完成，gui_callback属性: {hasattr(bot, 'gui_callback')}", flush=True)
    
    # 设置回调
    bot.gui_callback = test_callback
    print(f"[DEBUG] 回调设置完成，gui_callback: {bot.gui_callback}", flush=True)
    
    # 运行机器人
    try:
        bot.application.run_polling()
    except KeyboardInterrupt:
        print("\n程序已退出", flush=True)

if __name__ == "__main__":
    main()
