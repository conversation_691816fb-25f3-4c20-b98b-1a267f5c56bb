#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
混合方案测试：Selenium 绕过 + CloudScraper 复用
"""

import cloudscraper
import json
import time
import logging
from datetime import datetime
from cloudflare_bypass_helper import fetch_page_data, load_cookies_from_file

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class HybridCloudflareBypass:
    def __init__(self):
        self.scraper = None
        self.cookies_valid = False
        self.last_bypass_time = None
        
    def setup_scraper(self):
        """设置 CloudScraper"""
        self.scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            }
        )
        logging.info("✅ CloudScraper 创建成功")
        
    def bypass_with_selenium(self, url):
        """使用 Selenium 绕过 Cloudflare"""
        logging.info("🔄 使用 Selenium 绕过 Cloudflare...")
        
        start_time = time.time()
        result = fetch_page_data(url)
        end_time = time.time()
        
        if result:
            logging.info(f"✅ Selenium 绕过成功 (耗时 {end_time - start_time:.2f} 秒)")
            self.last_bypass_time = datetime.now()
            return True
        else:
            logging.error("❌ Selenium 绕过失败")
            return False
    
    def inject_cookies_to_scraper(self, domain='gmgn.ai'):
        """将 Selenium 获取的 cookies 注入到 CloudScraper"""
        cookies = load_cookies_from_file(domain)
        
        if not cookies:
            logging.error("❌ 无法加载 cookies")
            return False
        
        # 清除旧 cookies
        self.scraper.cookies.clear()
        
        # 注入新 cookies
        for name, value in cookies.items():
            self.scraper.cookies.set(name, value, domain=domain)
        
        logging.info(f"✅ 已注入 {len(cookies)} 个 cookies: {list(cookies.keys())}")
        self.cookies_valid = True
        return True
    
    def test_scraper_with_cookies(self, url):
        """使用注入了 cookies 的 CloudScraper 测试请求"""
        if not self.cookies_valid:
            logging.error("❌ Cookies 未设置或无效")
            return None
        
        try:
            logging.info(f"📡 CloudScraper 请求: {url}")
            
            # 添加必要的请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Referer': 'https://gmgn.ai/',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Cache-Control': 'max-age=0'
            }
            
            start_time = time.time()
            response = self.scraper.get(url, headers=headers, timeout=15)
            end_time = time.time()
            
            request_time = end_time - start_time
            
            logging.info(f"响应状态码: {response.status_code}")
            logging.info(f"响应时间: {request_time:.2f} 秒")
            
            if response.status_code == 200:
                # 检查是否被 Cloudflare 拦截
                if "Just a moment" in response.text or "Checking your browser" in response.text:
                    logging.warning("⚠️ 仍然被 Cloudflare 拦截")
                    self.cookies_valid = False
                    return None
                
                # 尝试解析数据
                try:
                    content_type = response.headers.get('content-type', '')
                    
                    if 'application/json' in content_type:
                        data = response.json()
                        logging.info("✅ 成功获取 JSON 数据")
                        return {
                            'success': True,
                            'data': data,
                            'time': request_time,
                            'method': 'CloudScraper'
                        }
                    else:
                        # HTML 响应，尝试提取 JSON
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(response.text, 'html.parser')
                        pre_content = soup.find('pre')
                        
                        if pre_content and pre_content.text.strip():
                            data = json.loads(pre_content.text)
                            logging.info("✅ 从 HTML 提取 JSON 数据")
                            return {
                                'success': True,
                                'data': data,
                                'time': request_time,
                                'method': 'CloudScraper'
                            }
                        else:
                            logging.warning("❌ HTML 中未找到 JSON 数据")
                            return None
                            
                except json.JSONDecodeError as e:
                    logging.error(f"❌ JSON 解析失败: {e}")
                    return None
                    
            elif response.status_code == 403:
                logging.warning("⚠️ 403 错误，cookies 可能已失效")
                self.cookies_valid = False
                return None
            else:
                logging.error(f"❌ HTTP 错误: {response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"❌ 请求异常: {e}")
            return None
    
    def smart_request(self, url):
        """智能请求：自动处理 cookies 失效"""
        # 如果 cookies 无效，先绕过
        if not self.cookies_valid:
            if not self.bypass_with_selenium(url):
                return None
            if not self.inject_cookies_to_scraper():
                return None
        
        # 尝试使用 CloudScraper 请求
        result = self.test_scraper_with_cookies(url)
        
        # 如果失败且是 cookies 问题，重新绕过
        if result is None and not self.cookies_valid:
            logging.info("🔄 Cookies 失效，重新绕过...")
            if self.bypass_with_selenium(url):
                if self.inject_cookies_to_scraper():
                    result = self.test_scraper_with_cookies(url)
        
        return result

def test_hybrid_approach():
    """测试混合方案"""
    print("🧪 测试混合方案：Selenium 绕过 + CloudScraper 复用")
    print("=" * 60)
    
    # 测试 URLs
    test_urls = [
        "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10",
        "https://gmgn.ai/defi/quotation/v1/tokens/sol?limit=5",
    ]
    
    bypass = HybridCloudflareBypass()
    bypass.setup_scraper()
    
    results = []
    total_start_time = time.time()
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n📋 测试 {i}/{len(test_urls)}: {url}")
        print("-" * 40)
        
        result = bypass.smart_request(url)
        
        if result and result['success']:
            data = result['data']
            data_count = 0
            
            if isinstance(data, dict) and 'data' in data:
                if isinstance(data['data'], list):
                    data_count = len(data['data'])
                else:
                    data_count = 1
            
            print(f"✅ 成功获取数据，包含 {data_count} 条记录")
            print(f"⏱️  耗时: {result['time']:.2f} 秒 (使用 {result['method']})")
            
            # 显示第一条记录
            if isinstance(data, dict) and 'data' in data and isinstance(data['data'], list) and len(data['data']) > 0:
                first_item = data['data'][0]
                if isinstance(first_item, dict):
                    symbol = first_item.get('symbol', first_item.get('name', 'Unknown'))
                    price = first_item.get('price', 'N/A')
                    print(f"第一条记录: {symbol}, 价格: {price}")
            
            results.append({
                'url': url,
                'success': True,
                'data_count': data_count,
                'time': result['time'],
                'method': result['method']
            })
        else:
            print(f"❌ 获取失败")
            results.append({
                'url': url,
                'success': False,
                'time': 0,
                'method': 'Failed'
            })
        
        # 短暂延迟
        time.sleep(1)
    
    total_end_time = time.time()
    total_time = total_end_time - total_start_time
    
    # 显示总结
    print(f"\n📊 混合方案测试总结:")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    total_data = sum(r.get('data_count', 0) for r in results if r['success'])
    avg_time = sum(r['time'] for r in results if r['success']) / success_count if success_count > 0 else 0
    
    print(f"成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    print(f"总耗时: {total_time:.2f} 秒")
    print(f"平均请求时间: {avg_time:.2f} 秒")
    print(f"总数据量: {total_data} 条记录")
    
    # 方法统计
    cloudscraper_count = sum(1 for r in results if r.get('method') == 'CloudScraper')
    print(f"\n📈 方法统计:")
    print(f"  CloudScraper 成功: {cloudscraper_count} 次")
    print(f"  总请求: {len(results)} 次")
    
    if success_count == len(results):
        print(f"\n🎉 混合方案完全成功！")
        print(f"💡 建议：一次 Selenium 绕过，后续使用 CloudScraper 快速请求")
        return True
    else:
        print(f"\n⚠️ 混合方案部分成功，需要进一步优化")
        return False

if __name__ == "__main__":
    test_hybrid_approach()
