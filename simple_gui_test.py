#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的GUI测试版本
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
from datetime import datetime
from TelegramBot import TelegramBot

class SimpleGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("简单GUI测试")
        self.root.geometry("600x400")
        
        # 创建日志区域
        self.log_text = scrolledtext.ScrolledText(self.root, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建按钮
        self.start_btn = tk.Button(self.root, text="启动机器人", command=self.start_bot)
        self.start_btn.pack(pady=5)
        
        self.bot = None
        self.bot_thread = None
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
    def start_bot(self):
        """启动机器人"""
        if self.bot is None:
            self.log_message("🚀 正在启动机器人...")
            
            # 创建机器人
            BOT_TOKEN = "7616525518:AAGWbdc9y5pu9o9UVFkOEnhXN7Yk8TJVY-E"
            self.bot = TelegramBot(BOT_TOKEN)
            
            # 设置回调
            def gui_callback(message):
                print(f"[控制台] {message}")  # 先在控制台测试
                # 线程安全的GUI更新
                self.root.after(0, lambda: self.log_message(message))
            
            self.bot.gui_callback = gui_callback
            self.log_message("✅ 回调设置完成")
            
            # 在新线程中运行机器人
            def run_bot():
                try:
                    import asyncio
                    # 为新线程创建事件循环
                    asyncio.set_event_loop(asyncio.new_event_loop())
                    self.root.after(0, lambda: self.log_message("🔄 正在连接Telegram服务器..."))
                    self.bot.application.run_polling()
                except Exception as e:
                    import traceback
                    error_msg = f"❌ 机器人运行错误: {str(e)}\n{traceback.format_exc()}"
                    print(f"[控制台错误] {error_msg}")
                    self.root.after(0, lambda: self.log_message(error_msg))
            
            self.bot_thread = threading.Thread(target=run_bot, daemon=True)
            self.bot_thread.start()
            self.log_message("✅ 机器人启动成功！请在Telegram中测试命令")
            
            self.start_btn.config(text="机器人运行中", state="disabled")
    
    def run(self):
        self.log_message("🎯 简单GUI测试启动")
        self.log_message("点击'启动机器人'按钮开始测试")
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleGUI()
    app.run()
