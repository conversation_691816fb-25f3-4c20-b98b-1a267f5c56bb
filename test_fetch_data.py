import logging
from cloudflare_bypass_helper import fetch_page_data
from gmgn_fetcher import filter_bluechip_tokens,format_bluechip_tokens,ca_format_tg_message
import json  # 确保导入 json 模块

def save_data_to_json(data, filename="bluechip_data.json"):
    """
    将数据保存为 JSON 文件
    :param data: 要保存的数据
    :param filename: 保存的文件名
    """
    try:
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        logging.info(f"数据已保存到文件: {filename}")
    except Exception as e:
        logging.error(f"保存数据到文件时出错: {e}")

def fetch_and_process_rug_data(address):
    """
    根据代币地址调用 `devrug` 接口并处理响应数据
    :param address: 代币地址
    :return: 解析后的 Rug 数据
    """
    devrug_url = f"https://gmgn.ai/defi/quotation/v1/tokens/rug_history/sol/{address}"
    logging.info(f"调用 Rug 数据接口: {devrug_url}")
    response = fetch_page_data(devrug_url)

    if response and "data" in response:
        rug_data = response["data"]
        logging.info(f"获取的 Rug 数据: {rug_data}")
        return rug_data
    else:
        logging.warning(f"未能获取到地址 {address} 的 Rug 数据。")
        return None

def test_fetch_data():
    """
    测试从新接口获取蓝筹代币数据并筛选
    """
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 蓝筹代币接口 URL
    url = "https://gmgn.ai/api/v1/bluechip_rank/sol?filters=renounced&filters=frozen&filters=not_wash_trading&order_by=volume&interval=1m&direction=desc&limit=10"
    logging.info("开始测试蓝筹代币数据获取...")
    response  = fetch_page_data(url)
     # 保存数据到 JSON 文件
    # save_data_to_json(response , "bluechip_data.json")
    if response and "data" in response:
        tokens = response["data"]
    # #     # 打印 tokens 数据以确认结构
    # #     logging.info(f"获取的蓝筹代币数据: {tokens}")
    # # # 筛选交易量大于 1M 的代币
    filtered_tokens = filter_bluechip_tokens(tokens, min_volume=20_000)
    # logging.info(f"筛选后剩余 {len(filtered_tokens)} 条代币数据。")

    message = format_bluechip_tokens(filtered_tokens)

    # logging.info(f"筛选后 {len(message)} 代币数据。")   
    # 打印筛选后的代币数据
    print(message)



def test_fetch_data1():
    """
    测试从新接口获取蓝筹代币数据并筛选
    """
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    ca = "83kU8EiiAj5s6oiuP1oQLm1FYPc5MxDiNUcBbEg2bonk"
    url = f"https://gmgn.ai/_next/data/_pLFFWLu7OQ0hfgtjCi3I/sol/token/{ca}.json?chain=sol&token={ca}"

    logging.info("开始测试蓝筹代币数据获取...")
    response = fetch_page_data(url)

    if response and "pageProps" in response:
        token_info = response["pageProps"].get("tokenInfo", {})
        # 打印 tokens 数据以确认结构
        logging.info(f"获取的蓝筹代币数据: {token_info}")

        # 格式化为 Telegram 消息
        tg_message = ca_format_tg_message(token_info)
        print(tg_message)  # 打印消息内容

        # 这里可以添加代码发送消息到 Telegram

    else:
        logging.warning("未获取到有效的代币数据。")
if __name__ == "__main__":
    test_fetch_data()
